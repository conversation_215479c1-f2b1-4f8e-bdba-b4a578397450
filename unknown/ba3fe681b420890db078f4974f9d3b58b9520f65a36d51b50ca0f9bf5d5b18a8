"""
Management command to test autonomous workflow isolation.
Verifies that autonomous kebeles don't send ID cards to subcity.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import Tenant, TenantWorkflowConfig
from idcards.models import IDCard, IDCardStatus


class Command(BaseCommand):
    help = 'Test autonomous workflow isolation - verify cards stay within kebele'

    def add_arguments(self, parser):
        parser.add_argument(
            '--kebele-id',
            type=int,
            help='Test specific kebele ID (optional)',
        )
        parser.add_argument(
            '--fix-issues',
            action='store_true',
            help='Fix any issues found (reset problematic cards)',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🧪 Testing Autonomous Workflow Isolation'))
        self.stdout.write('=' * 60)
        
        kebele_id = options.get('kebele_id')
        fix_issues = options.get('fix_issues', False)
        
        if kebele_id:
            self.test_specific_kebele(kebele_id, fix_issues)
        else:
            self.test_all_autonomous_kebeles(fix_issues)
            
        self.stdout.write('\n' + '=' * 60)
        self.stdout.write(self.style.SUCCESS('✅ Autonomous workflow isolation test completed!'))

    def test_specific_kebele(self, kebele_id, fix_issues):
        """Test a specific kebele."""
        try:
            kebele = Tenant.objects.get(id=kebele_id, type='kebele')
            self.test_kebele_isolation(kebele, fix_issues)
        except Tenant.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'❌ Kebele with ID {kebele_id} not found'))

    def test_all_autonomous_kebeles(self, fix_issues):
        """Test all autonomous kebeles."""
        # Find autonomous kebeles
        autonomous_kebeles = []
        try:
            configs = TenantWorkflowConfig.objects.filter(workflow_type='autonomous')
            for config in configs:
                if config.tenant.type == 'kebele':
                    autonomous_kebeles.append(config.tenant)
            
            self.stdout.write(f"🔍 Found {len(autonomous_kebeles)} autonomous kebeles")
            
            if not autonomous_kebeles:
                self.stdout.write(self.style.WARNING("⚠️ No autonomous kebeles found."))
                self.stdout.write("   Use: python manage.py setup_autonomous_workflow --kebele-id <ID>")
                return
                
            # Test each autonomous kebele
            for kebele in autonomous_kebeles:
                self.test_kebele_isolation(kebele, fix_issues)
                
            # Test subcity visibility
            self.test_subcity_visibility()
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error testing autonomous workflow: {e}"))

    def test_kebele_isolation(self, kebele, fix_issues):
        """Test that a kebele's autonomous workflow is properly isolated."""
        self.stdout.write(f"\n🏢 Testing kebele: {kebele.name}")
        self.stdout.write(f"   Schema: {kebele.schema_name}")
        self.stdout.write(f"   Parent: {kebele.parent.name if kebele.parent else 'None'}")
        
        # Check workflow configuration
        try:
            config = kebele.workflow_config
            workflow_type = config.workflow_type
            self.stdout.write(f"   Workflow: {workflow_type}")
            
            if workflow_type != 'autonomous':
                self.stdout.write(self.style.WARNING(f"   ⚠️ Not autonomous - skipping"))
                return
                
        except Exception:
            self.stdout.write(self.style.WARNING(f"   ⚠️ No workflow config - assuming centralized"))
            return
        
        with schema_context(kebele.schema_name):
            # Count ID cards by status
            total_cards = IDCard.objects.count()
            approved_cards = IDCard.objects.filter(status=IDCardStatus.APPROVED).count()
            kebele_approved_cards = IDCard.objects.filter(status=IDCardStatus.KEBELE_APPROVED).count()
            
            self.stdout.write(f"   📊 ID Card Statistics:")
            self.stdout.write(f"      Total cards: {total_cards}")
            self.stdout.write(f"      APPROVED (final): {approved_cards}")
            self.stdout.write(f"      KEBELE_APPROVED (should be 0): {kebele_approved_cards}")
            
            issues_found = False
            
            # Check for KEBELE_APPROVED cards (should be 0 in autonomous)
            if kebele_approved_cards > 0:
                self.stdout.write(self.style.WARNING(
                    f"   ⚠️ WARNING: Found {kebele_approved_cards} cards with KEBELE_APPROVED status"
                ))
                self.stdout.write("      In autonomous workflow, cards should go directly to APPROVED status")
                issues_found = True
                
                if fix_issues:
                    self.fix_kebele_approved_cards(kebele)
            else:
                self.stdout.write(self.style.SUCCESS("   ✅ Good: No KEBELE_APPROVED cards found"))
            
            # Check if any cards have subcity approval data
            cards_with_subcity_approval = IDCard.objects.filter(
                subcity_approved_by__isnull=False
            ).count()
            
            if cards_with_subcity_approval > 0:
                self.stdout.write(self.style.WARNING(
                    f"   ⚠️ WARNING: Found {cards_with_subcity_approval} cards with subcity approval"
                ))
                self.stdout.write("      Autonomous kebeles should not involve subcity approval")
                issues_found = True
                
                if fix_issues:
                    self.fix_subcity_approval_data(kebele)
            else:
                self.stdout.write(self.style.SUCCESS("   ✅ Good: No subcity approval found"))
            
            if not issues_found:
                self.stdout.write(self.style.SUCCESS("   🎯 Autonomous workflow working correctly!"))

    def fix_kebele_approved_cards(self, kebele):
        """Fix cards stuck in KEBELE_APPROVED status."""
        with schema_context(kebele.schema_name):
            problematic_cards = IDCard.objects.filter(status=IDCardStatus.KEBELE_APPROVED)
            count = problematic_cards.count()
            
            if count > 0:
                self.stdout.write(f"   🔧 Fixing {count} KEBELE_APPROVED cards...")
                
                for card in problematic_cards:
                    # Convert to final APPROVED status for autonomous workflow
                    card.status = IDCardStatus.APPROVED
                    card.has_subcity_pattern = True  # Apply both patterns for final approval
                    card.save()
                    
                self.stdout.write(self.style.SUCCESS(f"   ✅ Fixed {count} cards - converted to APPROVED status"))

    def fix_subcity_approval_data(self, kebele):
        """Remove subcity approval data from autonomous kebele cards."""
        with schema_context(kebele.schema_name):
            problematic_cards = IDCard.objects.filter(subcity_approved_by__isnull=False)
            count = problematic_cards.count()
            
            if count > 0:
                self.stdout.write(f"   🔧 Removing subcity approval data from {count} cards...")
                
                for card in problematic_cards:
                    # Remove subcity approval data
                    card.subcity_approved_by = None
                    card.subcity_approved_at = None
                    card.subcity_approval_comment = None
                    card.save()
                    
                self.stdout.write(self.style.SUCCESS(f"   ✅ Cleaned {count} cards - removed subcity approval data"))

    def test_subcity_visibility(self):
        """Test that subcity admins can't see autonomous kebele cards."""
        self.stdout.write("\n🧪 Testing Subcity Admin Visibility")
        self.stdout.write("-" * 40)
        
        try:
            # Find subcities with autonomous child kebeles
            subcities = Tenant.objects.filter(type='subcity')
            
            for subcity in subcities:
                child_kebeles = Tenant.objects.filter(parent=subcity, type='kebele')
                autonomous_children = []
                centralized_children = []
                
                for kebele in child_kebeles:
                    try:
                        config = kebele.workflow_config
                        if config.workflow_type == 'autonomous':
                            autonomous_children.append(kebele)
                        else:
                            centralized_children.append(kebele)
                    except:
                        centralized_children.append(kebele)  # Default to centralized
                
                if autonomous_children:
                    self.stdout.write(f"\n🏢 Subcity: {subcity.name}")
                    self.stdout.write(f"   Autonomous children: {[k.name for k in autonomous_children]}")
                    self.stdout.write(f"   Centralized children: {[k.name for k in centralized_children]}")
                    
                    # Simulate what subcity admin would see (using our fixed logic)
                    total_visible_cards = 0
                    
                    for kebele in child_kebeles:
                        workflow_type = 'centralized'  # Default
                        try:
                            config = kebele.workflow_config
                            workflow_type = config.workflow_type
                        except:
                            pass
                        
                        if workflow_type == 'autonomous':
                            self.stdout.write(f"   ⏭️ Skipping autonomous kebele {kebele.name}")
                            continue
                            
                        with schema_context(kebele.schema_name):
                            visible_cards = IDCard.objects.filter(
                                status=IDCardStatus.KEBELE_APPROVED
                            ).count()
                            total_visible_cards += visible_cards
                            self.stdout.write(f"   📋 {kebele.name}: {visible_cards} visible cards")
                    
                    self.stdout.write(f"   📊 Total cards visible to subcity admin: {total_visible_cards}")
                    self.stdout.write(self.style.SUCCESS("   ✅ Autonomous kebele cards are properly hidden"))
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error testing subcity visibility: {e}"))
