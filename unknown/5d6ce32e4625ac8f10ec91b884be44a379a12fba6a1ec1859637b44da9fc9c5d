from rest_framework import serializers
from .models import (
    Citizen, EmergencyContact, Parent, Child, Spouse,
    Biometric, Photo
)


class EmergencyContactSerializer(serializers.ModelSerializer):
    """Serializer for emergency contacts"""

    class Meta:
        model = EmergencyContact
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

    def validate(self, data):
        if not data.get('phone') and not data.get('email'):
            raise serializers.ValidationError("At least one contact method (phone or email) must be provided.")
        return data


class ParentSerializer(serializers.ModelSerializer):
    """Serializer for parents"""

    class Meta:
        model = Parent
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')


class ChildSerializer(serializers.ModelSerializer):
    """Serializer for children"""

    class Meta:
        model = Child
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')


class SpouseSerializer(serializers.ModelSerializer):
    """Serializer for spouse"""

    class Meta:
        model = Spouse
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

    def validate(self, data):
        if not data.get('phone') and not data.get('email'):
            raise serializers.ValidationError("At least one contact method (phone or email) must be provided.")
        return data


class BiometricSerializer(serializers.ModelSerializer):
    """Serializer for biometrics"""

    class Meta:
        model = Biometric
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')


class PhotoSerializer(serializers.ModelSerializer):
    """Serializer for photos"""

    class Meta:
        model = Photo
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'upload_date')


class CitizenSerializer(serializers.ModelSerializer):
    """Serializer for reading citizen data with all related information"""

    # Related data (read-only)
    emergency_contacts = EmergencyContactSerializer(many=True, read_only=True)
    parents = ParentSerializer(many=True, read_only=True)
    children = ChildSerializer(many=True, read_only=True)
    spouse_records = SpouseSerializer(many=True, read_only=True)
    biometric_record = BiometricSerializer(read_only=True)
    photo_record = PhotoSerializer(read_only=True)

    # Computed fields
    full_name = serializers.ReadOnlyField(source='get_full_name')
    age = serializers.ReadOnlyField()

    class Meta:
        model = Citizen
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'digital_id')


class CitizenListSerializer(serializers.ModelSerializer):
    """Simplified serializer for citizen list view"""
    full_name = serializers.ReadOnlyField(source='get_full_name')
    age = serializers.ReadOnlyField()
    transfer_status = serializers.SerializerMethodField()
    has_id_card = serializers.SerializerMethodField()

    class Meta:
        model = Citizen
        fields = ['id', 'digital_id', 'first_name', 'middle_name', 'last_name', 'full_name',
                 'first_name_am', 'middle_name_am', 'last_name_am',  # Added Amharic name fields
                 'date_of_birth', 'age', 'gender', 'phone', 'email', 'is_active', 'created_at',
                 'transfer_status', 'has_id_card']  # Added transfer and ID card status

    def get_transfer_status(self, obj):
        """Get transfer status information"""
        if obj.transfer_status == 'transferred':
            return {
                'is_transferred': True,
                'status': 'transferred',
                'message': f'Transferred to {obj.transfer_destination_kebele_name or "another kebele"}',
                'transfer_date': obj.transfer_date.isoformat() if obj.transfer_date else None,
                'destination_kebele': obj.transfer_destination_kebele_name,
                'transfer_reason': obj.transfer_reason,
                'transfer_request_id': obj.transfer_request_id,
                'original_digital_id': obj.original_digital_id
            }
        elif not obj.is_active:
            return {
                'is_transferred': False,
                'status': 'inactive',
                'message': 'Inactive citizen'
            }
        return {
            'is_transferred': False,
            'status': 'active',
            'message': 'Active citizen'
        }

    def get_has_id_card(self, obj):
        """Check if the citizen has any ID cards"""
        try:
            from idcards.models import IDCard
            return IDCard.objects.filter(citizen=obj).exists()
        except:
            return False


class CitizenCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating citizens with nested family data"""

    # Nested data for creation (write-only)
    emergency_contacts_data = EmergencyContactSerializer(many=True, write_only=True, required=False)
    parents_data = ParentSerializer(many=True, write_only=True, required=False)
    children_data = ChildSerializer(many=True, write_only=True, required=False)
    spouse_data = SpouseSerializer(write_only=True, required=False)

    class Meta:
        model = Citizen
        fields = [
            # Basic citizen fields
            'first_name', 'middle_name', 'last_name',
            'first_name_am', 'middle_name_am', 'last_name_am',
            'date_of_birth', 'gender', 'blood_type', 'disability',
            'religion', 'nationality', 'subcity', 'kebele', 'ketena',
            'house_number', 'phone', 'email', 'status', 'marital_status',
            'employment', 'employee_type', 'organization_name',
            'region', 'is_resident', 'is_active',

            # Photo field
            'photo',

            # Fingerprint option fields
            'fingerprint_option', 'no_fingerprint_reason',

            # Nested data fields
            'emergency_contacts_data', 'parents_data',
            'children_data', 'spouse_data'
        ]
        read_only_fields = ('created_at', 'updated_at', 'digital_id')

    def create(self, validated_data):
        """Create citizen and handle nested data separately"""
        # Remove nested data from validated_data
        emergency_contacts_data = validated_data.pop('emergency_contacts_data', [])
        parents_data = validated_data.pop('parents_data', [])
        children_data = validated_data.pop('children_data', [])
        spouse_data = validated_data.pop('spouse_data', None)

        # Create the citizen
        citizen = Citizen.objects.create(**validated_data)

        # Create related objects
        self._create_emergency_contacts(citizen, emergency_contacts_data)
        self._create_parents(citizen, parents_data)
        self._create_children(citizen, children_data)
        if spouse_data:
            self._create_spouse(citizen, spouse_data)

        return citizen

    def _create_emergency_contacts(self, citizen, emergency_contacts_data):
        """Create emergency contacts for the citizen"""
        for contact_data in emergency_contacts_data:
            EmergencyContact.objects.create(citizen=citizen, **contact_data)

    def _create_parents(self, citizen, parents_data):
        """Create parents for the citizen"""
        for parent_data in parents_data:
            Parent.objects.create(citizen=citizen, **parent_data)

    def _create_children(self, citizen, children_data):
        """Create children for the citizen"""
        for child_data in children_data:
            Child.objects.create(citizen=citizen, **child_data)

    def _create_spouse(self, citizen, spouse_data):
        """Create spouse for the citizen"""
        Spouse.objects.create(citizen=citizen, **spouse_data)


# Simplified serializers for dropdown/selection purposes
class CitizenSimpleSerializer(serializers.ModelSerializer):
    """Simple serializer for citizen selection (used in dropdowns)"""
    full_name = serializers.ReadOnlyField(source='get_full_name')

    class Meta:
        model = Citizen
        fields = ['id', 'first_name', 'middle_name', 'last_name', 'full_name', 'digital_id', 'gender']
