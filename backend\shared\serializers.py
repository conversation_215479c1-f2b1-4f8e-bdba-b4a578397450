from rest_framework import serializers
from django.utils import translation
from .models import (
    Country,
    Region,
    Religion,
    CitizenStatus,
    MaritalStatus,
    DocumentType,
    EmploymentType,
    Relationship,
    CurrentStatus,
    BiometricType,
    Ketena
)


class TranslatedNameMixin:
    """Mixin to provide language-aware translation functionality"""

    def get_language_code(self):
        """Get the current language code from various sources"""
        request = self.context.get('request')

        if request:
            # Try to get language from query parameter
            language_code = request.GET.get('language')
            if language_code:
                return language_code

            # Try to get language from Accept-Language header
            language_code = request.META.get('HTTP_ACCEPT_LANGUAGE')
            if language_code:
                # Extract the first language code from Accept-Language header
                language_code = language_code.split(',')[0].split('-')[0].strip()
                return language_code

            # Try to get from Django's current language
            language_code = translation.get_language()
            if language_code:
                return language_code

        # Default to Amharic
        return 'am'

class CountrySerializer(TranslatedNameMixin, serializers.ModelSerializer):
    translated_name = serializers.SerializerMethodField()

    class Meta:
        model = Country
        fields = ['id', 'name', 'translated_name', 'code', 'capital_city', 'flag_image', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)


class RegionSerializer(TranslatedNameMixin, serializers.ModelSerializer):
    country_name = serializers.ReadOnlyField(source='country.name')
    translated_name = serializers.SerializerMethodField()
    translated_country_name = serializers.SerializerMethodField()

    class Meta:
        model = Region
        fields = ['id', 'name', 'translated_name', 'code', 'country', 'country_name', 'translated_country_name', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)

    def get_translated_country_name(self, obj):
        """Get the translated country name based on the request language"""
        language_code = self.get_language_code()
        return obj.country.get_translated_name(language_code)


class ReligionSerializer(TranslatedNameMixin, serializers.ModelSerializer):
    translated_name = serializers.SerializerMethodField()

    class Meta:
        model = Religion
        fields = ['id', 'name', 'translated_name', 'description', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)


class CitizenStatusSerializer(TranslatedNameMixin, serializers.ModelSerializer):
    translated_name = serializers.SerializerMethodField()

    class Meta:
        model = CitizenStatus
        fields = ['id', 'name', 'translated_name', 'description', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)


class MaritalStatusSerializer(TranslatedNameMixin, serializers.ModelSerializer):
    translated_name = serializers.SerializerMethodField()

    class Meta:
        model = MaritalStatus
        fields = ['id', 'name', 'translated_name', 'description', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)


class DocumentTypeSerializer(TranslatedNameMixin, serializers.ModelSerializer):
    translated_name = serializers.SerializerMethodField()

    class Meta:
        model = DocumentType
        fields = ['id', 'name', 'translated_name', 'description', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)


class EmploymentTypeSerializer(TranslatedNameMixin, serializers.ModelSerializer):
    translated_name = serializers.SerializerMethodField()

    class Meta:
        model = EmploymentType
        fields = ['id', 'name', 'translated_name', 'description', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)


class RelationshipSerializer(TranslatedNameMixin, serializers.ModelSerializer):
    translated_name = serializers.SerializerMethodField()

    class Meta:
        model = Relationship
        fields = ['id', 'name', 'translated_name', 'description', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)


class CurrentStatusSerializer(TranslatedNameMixin, serializers.ModelSerializer):
    translated_name = serializers.SerializerMethodField()

    class Meta:
        model = CurrentStatus
        fields = ['id', 'name', 'translated_name', 'description', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)


class BiometricTypeSerializer(TranslatedNameMixin, serializers.ModelSerializer):
    translated_name = serializers.SerializerMethodField()

    class Meta:
        model = BiometricType
        fields = ['id', 'name', 'translated_name', 'description', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)


class KetenaSerializer(TranslatedNameMixin, serializers.ModelSerializer):
    translated_name = serializers.SerializerMethodField()

    class Meta:
        model = Ketena
        fields = ['id', 'name', 'translated_name', 'code', 'description', 'is_active']

    def get_translated_name(self, obj):
        """Get the translated name based on the request language"""
        language_code = self.get_language_code()
        return obj.get_translated_name(language_code)
