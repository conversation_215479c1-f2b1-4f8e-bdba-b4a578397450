#!/bin/bash

# <PERSON>ript to fix backend middleware and push to Docker Hub
echo "🔧 Fixing Backend Middleware and Pushing to Docker Hub"
echo "====================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
DOCKER_HUB_USERNAME="aragawmebratu"
IMAGE_NAME="goid-production"
BACKEND_TAG="backend-middleware-fixed"

print_status "Configuration:"
print_status "  Docker Hub: $DOCKER_HUB_USERNAME"
print_status "  Image: $IMAGE_NAME"
print_status "  Tag: $BACKEND_TAG"
echo ""

# Check if we're in the right directory
if [ ! -f "backend/manage.py" ]; then
    print_error "backend/manage.py not found"
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_success "Found backend directory"

# Show the middleware fix
print_status "Backend middleware fix applied:"
echo "================================="
echo "📁 backend/tenants/middleware.py (public_paths):"
grep -A 2 -B 2 "public_paths.*system-settings" backend/tenants/middleware.py || echo "Fix not found - checking line 37:"
sed -n '35,40p' backend/tenants/middleware.py
echo "================================="
echo ""

# Verify the fixes are applied
if grep -q "/api/tenants/system-settings/" backend/tenants/middleware.py && grep -q "/api/kebele-dashboard/" backend/tenants/middleware.py; then
    print_success "✅ Middleware fixes confirmed:"
    print_success "  - /api/tenants/system-settings/ added to public_paths"
    print_success "  - /api/kebele-dashboard/ added to public_paths"
else
    print_error "❌ Middleware fixes not found in backend/tenants/middleware.py"
    print_error "Please ensure both fixes are applied before building"
    exit 1
fi

# Ask for confirmation
echo ""
read -p "🚀 Build and push backend image with middleware fix? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Build cancelled"
    exit 0
fi

# Build the backend image
print_status "Building backend Docker image..."
cd backend

# Build the image
docker build \
    -f Dockerfile.production \
    -t "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG" \
    .

if [ $? -eq 0 ]; then
    print_success "✅ Backend image built successfully"
else
    print_error "❌ Failed to build backend image"
    exit 1
fi

# Tag as latest as well
docker tag "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG" "$DOCKER_HUB_USERNAME/$IMAGE_NAME:backend-latest"

print_success "✅ Tagged image as backend-latest"

# Push to Docker Hub
print_status "Pushing images to Docker Hub..."

# Push the specific tag
docker push "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
if [ $? -eq 0 ]; then
    print_success "✅ Pushed $BACKEND_TAG"
else
    print_error "❌ Failed to push $BACKEND_TAG"
    exit 1
fi

# Push the latest tag
docker push "$DOCKER_HUB_USERNAME/$IMAGE_NAME:backend-latest"
if [ $? -eq 0 ]; then
    print_success "✅ Pushed backend-latest"
else
    print_error "❌ Failed to push backend-latest"
    exit 1
fi

cd ..

print_success "🎉 Backend image successfully built and pushed!"
echo ""
print_status "📋 Summary of changes:"
print_status "  ✅ Added /api/tenants/system-settings/ to public_paths in middleware"
print_status "  ✅ Added /api/kebele-dashboard/ to public_paths in middleware"
print_status "  ✅ System settings and kebele dashboard endpoints now bypass tenant middleware"
print_status "  ✅ Built and pushed Docker image with middleware fixes"
echo ""
print_status "🚀 Next steps:"
print_status "  1. Update your docker-compose.yml to use the new image:"
print_status "     image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
print_status "  2. Restart the backend service:"
print_status "     docker-compose pull backend && docker-compose up -d backend"
print_status "  3. Test the system settings API - it should now return 200 OK"
echo ""
print_status "🔗 Docker Hub images:"
print_status "  📦 $DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
print_status "  📦 $DOCKER_HUB_USERNAME/$IMAGE_NAME:backend-latest"

echo ""
print_success "Backend middleware is now fixed for dashboard APIs!"
print_status "The following endpoints will now work correctly:"
print_status "  - /api/tenants/system-settings/"
print_status "  - /api/kebele-dashboard/"
