@echo off
REM Build and Push GoID Docker Images with Fingerprint Next Button Fix
REM This script builds both frontend and backend images and pushes them to Docker Hub

setlocal enabledelayedexpansion

echo 🚀 Building and Pushing GoID Docker Images with Fingerprint Fix...

REM Configuration
set DOCKER_USERNAME=aragawmebratu
set FRONTEND_IMAGE=%DOCKER_USERNAME%/goid-production:frontend-fingerprint-fix
set BACKEND_IMAGE=%DOCKER_USERNAME%/goid-production:backend-fingerprint-fix

echo.
echo 📋 Configuration:
echo Frontend Image: %FRONTEND_IMAGE%
echo Backend Image:  %BACKEND_IMAGE%
echo.

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

echo ✅ Docker is running

REM Build Frontend Image
echo.
echo 📦 Building Frontend Image...
echo Building: %FRONTEND_IMAGE%

cd frontend
docker build -f Dockerfile.production -t "%FRONTEND_IMAGE%" .
cd ..
if errorlevel 1 (
    echo ❌ ERROR: Failed to build frontend image
    pause
    exit /b 1
)

echo ✅ Frontend image built successfully

REM Build Backend Image
echo.
echo 📦 Building Backend Image...
echo Building: %BACKEND_IMAGE%

cd backend
docker build -f Dockerfile.production -t "%BACKEND_IMAGE%" .
cd ..
if errorlevel 1 (
    echo ❌ ERROR: Failed to build backend image
    pause
    exit /b 1
)

echo ✅ Backend image built successfully

REM Push Frontend Image
echo.
echo 🚀 Pushing Frontend Image to Docker Hub...
echo Pushing: %FRONTEND_IMAGE%

docker push "%FRONTEND_IMAGE%"
if errorlevel 1 (
    echo ❌ ERROR: Failed to push frontend image
    echo Make sure you're logged into Docker Hub: docker login
    pause
    exit /b 1
)

echo ✅ Frontend image pushed successfully

REM Push Backend Image
echo.
echo 🚀 Pushing Backend Image to Docker Hub...
echo Pushing: %BACKEND_IMAGE%

docker push "%BACKEND_IMAGE%"
if errorlevel 1 (
    echo ❌ ERROR: Failed to push backend image
    echo Make sure you're logged into Docker Hub: docker login
    pause
    exit /b 1
)

echo ✅ Backend image pushed successfully

REM Summary
echo.
echo 🎉 Build and Push Complete!
echo ================================
echo Frontend Image: %FRONTEND_IMAGE%
echo Backend Image:  %BACKEND_IMAGE%
echo.
echo 📋 Next Steps:
echo 1. SSH to your server
echo 2. Update docker-compose.yml to use the new images:
echo    - frontend: %FRONTEND_IMAGE%
echo    - backend:  %BACKEND_IMAGE%
echo 3. Run: docker-compose pull ^&^& docker-compose up -d
echo.
echo 🔧 Changes in this build:
echo - Fixed fingerprint registration 'Next' button issue
echo - Improved validation logic for 'without fingerprint' option
echo - Enhanced real-time validation feedback
echo - Better error messages for validation failures
echo - Added safe migration to handle existing fingerprint columns
echo - Added management command to fix migration conflicts
echo.

pause
