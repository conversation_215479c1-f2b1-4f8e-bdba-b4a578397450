import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from django.utils import timezone
from django.core.mail import EmailMessage
from django.conf import settings
from django.contrib.auth.models import User
from django.core.files.base import ContentFile

from tenants.models import Tenant
from .models import ReportSchedule, Report, ReportStatus, ReportAccess
from .services import ReportDataService
from .generators import get_report_generator

logger = logging.getLogger(__name__)


class ReportSchedulerService:
    """Service for managing scheduled report generation"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def process_scheduled_reports(self) -> Dict[str, Any]:
        """Process all due scheduled reports"""
        now = timezone.now()
        
        # Get all active schedules that are due
        due_schedules = ReportSchedule.objects.filter(
            is_active=True,
            next_run__lte=now
        )
        
        results = {
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'errors': []
        }
        
        for schedule in due_schedules:
            try:
                self.logger.info(f"Processing scheduled report: {schedule.name}")
                self._generate_scheduled_report(schedule)
                results['successful'] += 1
                
                # Update schedule timing
                schedule.last_run = now
                schedule.next_run = self._calculate_next_run(schedule)
                schedule.save()
                
            except Exception as e:
                self.logger.error(f"Failed to process schedule {schedule.name}: {str(e)}")
                results['failed'] += 1
                results['errors'].append({
                    'schedule': schedule.name,
                    'error': str(e)
                })
            
            results['processed'] += 1
        
        return results
    
    def _generate_scheduled_report(self, schedule: ReportSchedule) -> Report:
        """Generate a report from a schedule"""
        # Create report record
        report = Report.objects.create(
            title=f"{schedule.name} - {timezone.now().strftime('%Y-%m-%d %H:%M')}",
            description=f"Automatically generated report from schedule: {schedule.name}",
            report_type=schedule.template.report_type,
            template=schedule.template,
            format=schedule.format,
            filters=schedule.template.default_filters,
            parameters={},
            generated_by=schedule.created_by,
            status=ReportStatus.PROCESSING
        )
        
        try:
            # For scheduled reports, we need to determine the tenant
            # This is a simplified approach - in production, you might want to
            # associate schedules with specific tenants
            tenant = self._get_default_tenant()
            
            # Generate report data
            data_service = ReportDataService(tenant)
            
            if schedule.template.report_type == 'demographic':
                report_data = data_service.get_demographic_data(schedule.template.default_filters)
            elif schedule.template.report_type == 'registration_trends':
                report_data = data_service.get_registration_trends(schedule.template.default_filters)
            elif schedule.template.report_type == 'id_card_status':
                report_data = data_service.get_id_card_status_report(schedule.template.default_filters)
            elif schedule.template.report_type == 'service_access':
                report_data = data_service.get_service_access_analytics(schedule.template.default_filters)
            elif schedule.template.report_type == 'migration_analysis':
                report_data = data_service.get_migration_analysis(schedule.template.default_filters)
            else:
                raise ValueError(f"Unsupported report type: {schedule.template.report_type}")
            
            # Generate report file
            generator = get_report_generator(schedule.template.report_type, tenant, report_data)
            file_buffer = generator.generate(schedule.format)
            
            # Save file
            file_extension = {
                'pdf': 'pdf',
                'excel': 'xlsx',
                'csv': 'csv',
                'json': 'json'
            }.get(schedule.format, 'pdf')
            
            filename = f"scheduled_{report.id}_{schedule.template.report_type}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
            
            report.file.save(
                filename,
                ContentFile(file_buffer.getvalue()),
                save=False
            )
            
            # Update report metadata
            report.file_size = len(file_buffer.getvalue())
            report.total_records = (
                report_data.get('total_citizens', 0) or 
                report_data.get('total_registrations', 0) or 
                report_data.get('total_id_cards', 0)
            )
            report.summary_data = {
                'key_metrics': self._extract_key_metrics(report_data, schedule.template.report_type)
            }
            
            # Set expiration (30 days from now)
            report.expires_at = timezone.now() + timedelta(days=30)
            
            report.mark_as_completed(processing_time=timezone.now() - report.created_at)
            
            # Send email notifications
            if schedule.email_recipients:
                self._send_report_email(report, schedule)
            
            return report
            
        except Exception as e:
            report.mark_as_failed(str(e))
            raise
    
    def _get_default_tenant(self) -> Tenant:
        """Get default tenant for scheduled reports"""
        # This is a simplified approach - in production, you might want to
        # associate schedules with specific tenants or run reports for all tenants
        return Tenant.objects.filter(schema_name='public').first() or Tenant.objects.first()
    
    def _calculate_next_run(self, schedule: ReportSchedule) -> datetime:
        """Calculate next run time based on cron expression"""
        # This is a simplified implementation
        # In production, use a proper cron parser like croniter
        
        now = timezone.now()
        
        # Simple patterns for common schedules
        if schedule.cron_expression == '0 9 * * *':  # Daily at 9 AM
            return now.replace(hour=9, minute=0, second=0, microsecond=0) + timedelta(days=1)
        elif schedule.cron_expression == '0 9 * * 1':  # Weekly on Monday at 9 AM
            days_ahead = 0 - now.weekday()  # Monday is 0
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            return now.replace(hour=9, minute=0, second=0, microsecond=0) + timedelta(days=days_ahead)
        elif schedule.cron_expression == '0 9 1 * *':  # Monthly on 1st at 9 AM
            if now.day == 1 and now.hour < 9:
                return now.replace(hour=9, minute=0, second=0, microsecond=0)
            else:
                # Next month
                if now.month == 12:
                    return now.replace(year=now.year + 1, month=1, day=1, hour=9, minute=0, second=0, microsecond=0)
                else:
                    return now.replace(month=now.month + 1, day=1, hour=9, minute=0, second=0, microsecond=0)
        else:
            # Default: add 1 day
            return now + timedelta(days=1)
    
    def _send_report_email(self, report: Report, schedule: ReportSchedule):
        """Send email notification with report attachment"""
        try:
            subject = f"Scheduled Report: {report.title}"
            message = f"""
            Dear Recipient,
            
            Your scheduled report "{report.title}" has been generated successfully.
            
            Report Details:
            - Type: {report.get_report_type_display()}
            - Format: {report.get_format_display()}
            - Generated: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}
            - Records: {report.total_records or 'N/A'}
            
            Please find the report attached to this email.
            
            Best regards,
            GoID System
            """
            
            email = EmailMessage(
                subject=subject,
                body=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=schedule.email_recipients
            )
            
            # Attach report file
            if report.file:
                email.attach_file(report.file.path)
            
            email.send()
            
            self.logger.info(f"Report email sent for schedule: {schedule.name}")
            
        except Exception as e:
            self.logger.error(f"Failed to send report email for schedule {schedule.name}: {str(e)}")
    
    def _extract_key_metrics(self, report_data: dict, report_type: str) -> dict:
        """Extract key metrics from report data for quick access"""
        if report_type == 'demographic':
            return {
                'total_citizens': report_data.get('total_citizens', 0),
                'gender_ratio': report_data.get('gender_distribution', []),
                'age_groups': len(report_data.get('age_group_distribution', []))
            }
        elif report_type == 'registration_trends':
            return {
                'total_registrations': report_data.get('total_registrations', 0),
                'average_daily': report_data.get('average_daily', 0),
                'trend_months': len(report_data.get('monthly_trends', []))
            }
        elif report_type == 'id_card_status':
            return {
                'total_cards': report_data.get('total_id_cards', 0),
                'coverage_percentage': report_data.get('coverage_percentage', 0),
                'expiring_cards': report_data.get('expiring_cards', 0)
            }
        return {}


class ReportCleanupService:
    """Service for cleaning up expired reports"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def cleanup_expired_reports(self) -> Dict[str, Any]:
        """Clean up expired reports and their files"""
        now = timezone.now()
        
        # Find expired reports
        expired_reports = Report.objects.filter(
            expires_at__lt=now,
            status=ReportStatus.COMPLETED
        )
        
        results = {
            'processed': 0,
            'deleted': 0,
            'errors': []
        }
        
        for report in expired_reports:
            try:
                # Delete file if exists
                if report.file:
                    report.file.delete(save=False)
                
                # Update status to expired
                report.status = ReportStatus.EXPIRED
                report.save()
                
                results['deleted'] += 1
                self.logger.info(f"Cleaned up expired report: {report.title}")
                
            except Exception as e:
                self.logger.error(f"Failed to cleanup report {report.title}: {str(e)}")
                results['errors'].append({
                    'report': report.title,
                    'error': str(e)
                })
            
            results['processed'] += 1
        
        return results
