# 🚀 GoID Nginx Deployment

## 📋 Overview

This nginx-based deployment resolves all the tenant routing issues and provides a professional, scalable architecture using port 80.

## 🎯 Quick Start

### **1. Upload Files to Server**
Upload these nginx files to your server:
- `nginx.conf`
- `docker-compose.nginx.yml`
- `deploy-nginx.sh`

### **2. Run Deployment**
```bash
ssh administrator@10.139.8.162
cd goid-deployment
chmod +x deploy-nginx.sh
bash deploy-nginx.sh
```

### **3. Verify Deployment**
```bash
# Test main application
curl http://10.139.8.162/

# Test API (this should now work!)
curl http://10.139.8.162/api/common/language/info/

# Test admin
curl -I http://10.139.8.162/admin/
```

## 🌐 Service URLs After Migration

| Service | Before | After |
|---------|--------|-------|
| Frontend | http://10.139.8.162:3000 | http://10.139.8.162 |
| Backend API | http://10.139.8.162:8000/api/ | http://10.139.8.162/api/ |
| Django Admin | http://10.139.8.162:8000/admin/ | http://10.139.8.162/admin/ |
| PgAdmin | http://10.139.8.162:5050 | http://10.139.8.162:5050 |

## ✅ Benefits

- ✅ **Fixes tenant routing issues** - No more 404 errors for API calls
- ✅ **Professional URLs** - No port numbers needed
- ✅ **Single entry point** - Everything through port 80
- ✅ **SSL ready** - Easy HTTPS setup later
- ✅ **Better performance** - Nginx handles static files efficiently

## 🔧 Architecture

```
Internet (Port 80) → Nginx → {
    /api/* → Backend (Port 8000)
    /admin/* → Backend (Port 8000)
    /* → Frontend (Port 80)
}
```

## 🆘 Troubleshooting

```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs -f nginx
docker-compose logs -f backend

# Test health
curl http://10.139.8.162/health
```

## 📞 Next Steps

1. Test the application at http://10.139.8.162
2. Verify API calls work without CORS errors
3. Set up SSL with `bash setup-ssl.sh goid.uog.edu.et`
4. Update DNS to point goid.uog.edu.et to your server

This setup will completely resolve the API access issues you've been experiencing!
