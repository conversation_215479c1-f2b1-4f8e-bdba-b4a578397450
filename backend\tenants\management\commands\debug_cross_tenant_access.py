"""
Management command to debug cross-tenant ID card access issues.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import Tenant
from idcards.models import IDCard
from users.models import User


class Command(BaseCommand):
    help = 'Debug cross-tenant ID card access issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user-email',
            type=str,
            help='User email to test',
            required=True
        )
        parser.add_argument(
            '--tenant-id',
            type=int,
            help='Tenant ID to test',
            required=True
        )
        parser.add_argument(
            '--id-card-id',
            type=int,
            help='ID card ID to test',
            required=True
        )

    def handle(self, *args, **options):
        user_email = options['user_email']
        tenant_id = options['tenant_id']
        id_card_id = options['id_card_id']
        
        self.stdout.write(self.style.SUCCESS('🔍 Debugging Cross-Tenant ID Card Access'))
        self.stdout.write('=' * 60)
        
        # Test user permissions
        self.test_user_permissions(user_email)
        
        # Test tenant structure
        self.test_tenant_structure(tenant_id)
        
        # Test ID card location
        self.test_id_card_location(id_card_id, tenant_id)

    def test_user_permissions(self, user_email):
        """Test user permissions and role."""
        self.stdout.write(f"\n👤 Testing User: {user_email}")
        self.stdout.write("-" * 40)
        
        try:
            user = User.objects.get(email=user_email)
            self.stdout.write(f"✅ User found: {user.email}")
            self.stdout.write(f"   Role: {user.role}")
            self.stdout.write(f"   Is superuser: {user.is_superuser}")
            self.stdout.write(f"   Is active: {user.is_active}")
            self.stdout.write(f"   Tenant: {user.tenant.name if user.tenant else 'None'}")
            self.stdout.write(f"   Tenant type: {user.tenant.type if user.tenant else 'None'}")
            
            # Check if role is allowed for cross-tenant access
            allowed_roles = ['subcity_admin', 'city_admin', 'superadmin', 'print_id_cards', 'designated_printer']
            if user.role in allowed_roles or user.is_superuser:
                self.stdout.write(self.style.SUCCESS(f"✅ User role '{user.role}' is allowed for cross-tenant access"))
            else:
                self.stdout.write(self.style.ERROR(f"❌ User role '{user.role}' is NOT allowed for cross-tenant access"))
                self.stdout.write(f"   Allowed roles: {allowed_roles}")
                
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"❌ User not found: {user_email}"))

    def test_tenant_structure(self, tenant_id):
        """Test tenant structure and child kebeles."""
        self.stdout.write(f"\n🏢 Testing Tenant Structure: {tenant_id}")
        self.stdout.write("-" * 40)
        
        try:
            tenant = Tenant.objects.get(id=tenant_id)
            self.stdout.write(f"✅ Tenant found: {tenant.name}")
            self.stdout.write(f"   Type: {tenant.type}")
            self.stdout.write(f"   Schema: {tenant.schema_name}")
            self.stdout.write(f"   Parent: {tenant.parent.name if tenant.parent else 'None'}")
            
            # Check if tenant type is valid for cross-tenant access
            if tenant.type in ['subcity', 'city']:
                self.stdout.write(self.style.SUCCESS(f"✅ Tenant type '{tenant.type}' is valid for cross-tenant access"))
                
                # Get child kebeles
                if tenant.type == 'subcity':
                    child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')
                else:  # city
                    child_subcities = Tenant.objects.filter(parent=tenant, type='subcity')
                    child_kebeles = Tenant.objects.filter(parent__in=child_subcities, type='kebele')
                
                self.stdout.write(f"📋 Found {child_kebeles.count()} child kebeles:")
                for kebele in child_kebeles:
                    self.stdout.write(f"   - {kebele.name} (ID: {kebele.id}, Schema: {kebele.schema_name})")
                    
            else:
                self.stdout.write(self.style.ERROR(f"❌ Tenant type '{tenant.type}' is NOT valid for cross-tenant access"))
                self.stdout.write("   Only 'subcity' and 'city' tenants can use cross-tenant endpoints")
                
        except Tenant.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"❌ Tenant not found: {tenant_id}"))

    def test_id_card_location(self, id_card_id, tenant_id):
        """Test where the ID card is located."""
        self.stdout.write(f"\n🆔 Testing ID Card Location: {id_card_id}")
        self.stdout.write("-" * 40)
        
        try:
            tenant = Tenant.objects.get(id=tenant_id)
            
            # Get child kebeles
            if tenant.type == 'subcity':
                child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')
            elif tenant.type == 'city':
                child_subcities = Tenant.objects.filter(parent=tenant, type='subcity')
                child_kebeles = Tenant.objects.filter(parent__in=child_subcities, type='kebele')
            else:
                # If it's a kebele, check in its own schema
                child_kebeles = [tenant]
            
            found_in_kebeles = []
            
            # Search in each kebele
            for kebele in child_kebeles:
                try:
                    with schema_context(kebele.schema_name):
                        id_cards = IDCard.objects.filter(id=id_card_id)
                        if id_cards.exists():
                            id_card = id_cards.first()
                            found_in_kebeles.append({
                                'kebele': kebele,
                                'id_card': id_card
                            })
                            self.stdout.write(f"✅ Found ID card {id_card_id} in kebele {kebele.name}")
                            self.stdout.write(f"   Card number: {id_card.card_number}")
                            self.stdout.write(f"   Status: {id_card.status}")
                            self.stdout.write(f"   Citizen: {id_card.citizen.get_full_name() if id_card.citizen else 'None'}")
                        else:
                            self.stdout.write(f"⚠️ ID card {id_card_id} not found in kebele {kebele.name}")
                            
                except Exception as e:
                    self.stdout.write(f"❌ Error searching in kebele {kebele.name}: {e}")
            
            if not found_in_kebeles:
                self.stdout.write(self.style.ERROR(f"❌ ID card {id_card_id} not found in any kebele"))
                
                # Also check if it exists in the current tenant's schema
                try:
                    with schema_context(tenant.schema_name):
                        id_cards = IDCard.objects.filter(id=id_card_id)
                        if id_cards.exists():
                            self.stdout.write(f"⚠️ ID card {id_card_id} found in {tenant.name} schema, but this is not a kebele")
                        else:
                            self.stdout.write(f"⚠️ ID card {id_card_id} not found in {tenant.name} schema either")
                except Exception as e:
                    self.stdout.write(f"❌ Error searching in {tenant.name} schema: {e}")
            else:
                self.stdout.write(self.style.SUCCESS(f"✅ ID card {id_card_id} found in {len(found_in_kebeles)} kebele(s)"))
                
        except Tenant.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"❌ Tenant not found: {tenant_id}"))
