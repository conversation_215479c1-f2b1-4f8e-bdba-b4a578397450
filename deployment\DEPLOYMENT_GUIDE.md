# 🚀 GoID Production Deployment Guide

## Current Issue Analysis

Based on your Docker Compose configuration, here are the potential issues preventing backend access:

### 1. **CORS Configuration Issue**
- Your Django settings had hardcoded CORS origins
- **FIXED**: Updated `backend/goid/settings.py` to read CORS origins from environment variables

### 2. **Environment Variable Override**
- Docker Compose environment variables weren't being properly used
- **FIXED**: Created `docker-compose.production-fixed.yml` with proper environment variable handling

### 3. **Missing Health Checks**
- No health checks to ensure services are ready before dependencies start
- **FIXED**: Added health checks for all services

## 🛠️ Quick Fix Steps

### Step 1: Upload Fixed Files to Your Server

Upload these files to your server:
- `deployment/docker-compose.production-fixed.yml`
- `deployment/.env.production`
- `deployment/quick-deploy.sh`
- `deployment/troubleshoot-backend.sh`
- `deployment/test-backend.sh`

### Step 2: Deploy with Fixed Configuration

```bash
# SSH to your server
ssh your-server

# Navigate to your deployment directory
cd /path/to/your/deployment

# Copy the fixed docker-compose file
cp docker-compose.production-fixed.yml docker-compose.yml

# Create environment file
cp .env.production .env

# Edit the environment file with your actual passwords
nano .env

# Run the quick deployment script
bash quick-deploy.sh
```

### Step 3: Test the Deployment

```bash
# Test backend connectivity
bash test-backend.sh

# If issues persist, run troubleshooting
bash troubleshoot-backend.sh
```

## 🔧 Manual Troubleshooting Steps

If the quick deployment doesn't work, follow these manual steps:

### 1. Check Container Status
```bash
docker-compose ps
docker-compose logs backend
```

### 2. Test Network Connectivity
```bash
# Test from inside container
docker exec $(docker-compose ps -q backend) curl -f http://localhost:8000/admin/

# Test from host
curl -f http://localhost:8000/admin/

# Test with server IP
curl -f http://************:8000/admin/
```

### 3. Check Environment Variables
```bash
docker exec $(docker-compose ps -q backend) env | grep -E "(DJANGO_|DEBUG|ALLOWED_HOSTS|CORS_)"
```

### 4. Check Database Connection
```bash
docker exec $(docker-compose ps -q backend) python manage.py check --database default
```

### 5. Check Firewall Settings
```bash
# Ubuntu/Debian
sudo ufw status
sudo ufw allow 8000

# CentOS/RHEL
sudo firewall-cmd --list-ports
sudo firewall-cmd --add-port=8000/tcp --permanent
sudo firewall-cmd --reload
```

## 🔍 Common Issues and Solutions

### Issue 1: "Connection Refused"
**Cause**: Backend container not running or not binding to correct port
**Solution**: 
```bash
docker-compose restart backend
docker-compose logs backend
```

### Issue 2: "CORS Error"
**Cause**: Frontend IP not in CORS allowed origins
**Solution**: Update `.env` file:
```bash
CORS_ALLOWED_ORIGINS=http://************:3000,http://your-domain:3000
```

### Issue 3: "Database Connection Error"
**Cause**: Database not ready or wrong credentials
**Solution**:
```bash
docker-compose restart db
docker-compose logs db
```

### Issue 4: "502 Bad Gateway"
**Cause**: Backend not responding to requests
**Solution**:
```bash
# Check if backend is healthy
docker exec $(docker-compose ps -q backend) python manage.py check
```

## 📋 Environment Variables Reference

Create a `.env` file with these variables:

```bash
# Database
DB_PASSWORD=your-secure-password

# Security
SECRET_KEY=your-very-long-secret-key

# Network (adjust to your server)
ALLOWED_HOSTS=************,goid.uog.edu.et,localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://************:3000,https://goid.uog.edu.et

# PgAdmin
PGADMIN_PASSWORD=your-pgadmin-password
```

## 🎯 Expected Results

After successful deployment:
- Frontend: http://************:3000
- Backend API: http://************:8000
- Django Admin: http://************:8000/admin/
- PgAdmin: http://************:5050

## 🆘 If You Need SSH Access

If you'd like me to help directly on the server:
1. Create a temporary SSH user for me
2. Provide SSH credentials
3. I can run the troubleshooting and fix the deployment directly

Let me know if you'd prefer this approach!
