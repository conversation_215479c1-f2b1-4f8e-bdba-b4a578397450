from django.http import HttpResponse, Http404
from django.utils import timezone
from django.core.files.base import ContentFile
from django.db.models import Q
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from tenants.permissions import CanManageTenants
from rest_framework import permissions
from django.db import connection
from tenants.models import Tenant
from django_tenants.utils import schema_context
from datetime import datetime, timedelta
import uuid
import os


class SuperAdminOrTenantPermission(permissions.BasePermission):
    """
    Permission class that allows superadmin users or users with tenant management permissions.
    Superadmin users can access reports from any tenant.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and superadmins have full permission
        if user.is_superuser or getattr(user, 'role', None) == 'superadmin':
            return True

        # For regular users, check tenant management permissions
        can_manage_tenants = CanManageTenants()
        return can_manage_tenants.has_permission(request, view)


class TenantAwareReportMixin:
    """
    Mixin to handle tenant context for reports.
    Superadmin users can access reports from any tenant by specifying tenant_id parameter.
    Regular users access reports from their own tenant.
    """

    def get_tenant_context(self):
        """Get the appropriate tenant context for the current user."""
        user = self.request.user

        # For superadmin users, check if tenant_id is provided in query params
        if user.is_superuser or getattr(user, 'role', None) == 'superadmin':
            tenant_id = self.request.query_params.get('tenant_id')
            if tenant_id:
                try:
                    return Tenant.objects.get(id=tenant_id)
                except Tenant.DoesNotExist:
                    return None
            # If no tenant_id provided, return None (will show message to specify tenant)
            return None

        # For regular users, use their tenant
        return getattr(user, 'tenant', None)

    def get_queryset(self):
        """Get queryset with appropriate tenant context."""
        tenant = self.get_tenant_context()

        if not tenant:
            # Return empty queryset if no tenant context
            return self.queryset.model.objects.none()

        # Switch to tenant schema and get queryset
        with schema_context(tenant.schema_name):
            return super().get_queryset()

    def list(self, request, *args, **kwargs):
        """Override list to handle tenant context."""
        try:
            # Simple test response first
            return Response({
                'message': 'Test response from TenantAwareReportMixin',
                'user': str(request.user),
                'is_superuser': request.user.is_superuser
            })
        except Exception as e:
            return Response({
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


from .models import Report, ReportTemplate, ReportSchedule, ReportAccess, ReportType, ReportFormat, ReportStatus
from .serializers import (
    ReportSerializer, ReportTemplateSerializer, ReportScheduleSerializer,
    ReportCreateSerializer, ReportFilterSerializer
)
from .services import ReportDataService
from .generators import get_report_generator
from .models import Report, ReportTemplate, ReportSchedule, ReportAccess, ReportType, ReportFormat, ReportStatus
from .serializers import (
    ReportSerializer, ReportTemplateSerializer, ReportScheduleSerializer,
    ReportCreateSerializer, ReportFilterSerializer
)
from .services import ReportDataService
from .generators import get_report_generator


class ReportTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for managing report templates"""
    queryset = ReportTemplate.objects.all()
    serializer_class = ReportTemplateSerializer
    permission_classes = [IsAuthenticated]

    def list(self, request, *args, **kwargs):
        """Override list to handle tenant context."""
        try:
            # Simple test response first
            return Response({
                'message': 'Test response from ReportTemplateViewSet',
                'user': str(request.user),
                'is_superuser': request.user.is_superuser
            })
        except Exception as e:
            return Response({
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_queryset(self):
        tenant = self.get_tenant_context()

        if not tenant:
            return ReportTemplate.objects.none()

        with schema_context(tenant.schema_name):
            return ReportTemplate.objects.filter(
                Q(is_system_template=True) | Q(created_by=self.request.user)
            ).filter(is_active=True)
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class ReportViewSet(TenantAwareReportMixin, viewsets.ModelViewSet):
    """ViewSet for managing reports"""
    queryset = Report.objects.all()
    serializer_class = ReportSerializer
    permission_classes = [SuperAdminOrTenantPermission]

    def get_queryset(self):
        tenant = self.get_tenant_context()

        if not tenant:
            return Report.objects.none()

        with schema_context(tenant.schema_name):
            # Users can only see their own reports or system-generated reports
            return Report.objects.filter(
                Q(generated_by=self.request.user) | Q(generated_by__isnull=True)
            ).order_by('-created_at')
    
    def get_serializer_class(self):
        if self.action == 'create':
            return ReportCreateSerializer
        return ReportSerializer
    
    @action(detail=False, methods=['post'], url_path='generate')
    def generate_report(self, request):
        """Generate a new report"""
        serializer = ReportCreateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        data = serializer.validated_data
        
        # Get tenant from request
        tenant_id = request.headers.get('X-Tenant-ID')
        if not tenant_id:
            return Response(
                {'error': 'Tenant ID is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            tenant = Tenant.objects.get(id=tenant_id)
        except Tenant.DoesNotExist:
            return Response(
                {'error': 'Invalid tenant'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create report record
        report = Report.objects.create(
            title=data['title'],
            description=data.get('description', ''),
            report_type=data['report_type'],
            template=data.get('template'),
            format=data['format'],
            filters=data.get('filters', {}),
            parameters=data.get('parameters', {}),
            period_start=data.get('period_start'),
            period_end=data.get('period_end'),
            generated_by=request.user,
            status=ReportStatus.PROCESSING
        )
        
        try:
            # Generate report data
            data_service = ReportDataService(tenant)
            
            if data['report_type'] == ReportType.DEMOGRAPHIC:
                report_data = data_service.get_demographic_data(data.get('filters', {}))
            elif data['report_type'] == ReportType.REGISTRATION_TRENDS:
                report_data = data_service.get_registration_trends(data.get('filters', {}))
            elif data['report_type'] == ReportType.ID_CARD_STATUS:
                report_data = data_service.get_id_card_status_report(data.get('filters', {}))
            elif data['report_type'] == ReportType.SERVICE_ACCESS:
                report_data = data_service.get_service_access_analytics(data.get('filters', {}))
            elif data['report_type'] == ReportType.MIGRATION_ANALYSIS:
                report_data = data_service.get_migration_analysis(data.get('filters', {}))
            else:
                raise ValueError(f"Unsupported report type: {data['report_type']}")
            
            # Generate report file
            generator = get_report_generator(data['report_type'], tenant, report_data)
            file_buffer = generator.generate(data['format'])
            
            # Save file
            file_extension = {
                ReportFormat.PDF: 'pdf',
                ReportFormat.EXCEL: 'xlsx',
                ReportFormat.CSV: 'csv',
                ReportFormat.JSON: 'json'
            }.get(data['format'], 'pdf')
            
            filename = f"{report.id}_{data['report_type']}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
            
            report.file.save(
                filename,
                ContentFile(file_buffer.getvalue()),
                save=False
            )
            
            # Update report metadata
            report.file_size = len(file_buffer.getvalue())
            report.total_records = report_data.get('total_citizens', 0) or report_data.get('total_registrations', 0) or report_data.get('total_id_cards', 0)
            report.summary_data = {
                'key_metrics': self._extract_key_metrics(report_data, data['report_type'])
            }
            
            # Set expiration (30 days from now)
            report.expires_at = timezone.now() + timedelta(days=30)
            
            report.mark_as_completed(processing_time=timezone.now() - report.created_at)
            
            return Response(ReportSerializer(report).data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            report.mark_as_failed(str(e))
            return Response(
                {'error': f'Report generation failed: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'], url_path='download')
    def download_report(self, request, pk=None):
        """Download a generated report"""
        report = self.get_object()
        
        if report.status != ReportStatus.COMPLETED:
            return Response(
                {'error': 'Report is not ready for download'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if not report.file:
            return Response(
                {'error': 'Report file not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        if report.is_expired:
            return Response(
                {'error': 'Report has expired'}, 
                status=status.HTTP_410_GONE
            )
        
        # Log access
        ReportAccess.objects.create(
            report=report,
            user=request.user,
            action='download',
            ip_address=self._get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        # Determine content type
        content_types = {
            ReportFormat.PDF: 'application/pdf',
            ReportFormat.EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ReportFormat.CSV: 'text/csv',
            ReportFormat.JSON: 'application/json'
        }
        
        content_type = content_types.get(report.format, 'application/octet-stream')
        
        # Create response
        response = HttpResponse(report.file.read(), content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{os.path.basename(report.file.name)}"'
        response['Content-Length'] = report.file_size or len(response.content)
        
        return response
    
    @action(detail=False, methods=['get'], url_path='types')
    def get_report_types(self, request):
        """Get available report types"""
        types = [
            {
                'value': choice[0],
                'label': choice[1],
                'description': self._get_report_type_description(choice[0])
            }
            for choice in ReportType.choices
        ]
        return Response(types)
    
    @action(detail=False, methods=['get'], url_path='formats')
    def get_report_formats(self, request):
        """Get available report formats"""
        formats = [
            {
                'value': choice[0],
                'label': choice[1],
                'description': self._get_format_description(choice[0])
            }
            for choice in ReportFormat.choices
        ]
        return Response(formats)
    
    def _extract_key_metrics(self, report_data: dict, report_type: str) -> dict:
        """Extract key metrics from report data for quick access"""
        if report_type == ReportType.DEMOGRAPHIC:
            return {
                'total_citizens': report_data.get('total_citizens', 0),
                'gender_ratio': report_data.get('gender_distribution', []),
                'age_groups': len(report_data.get('age_group_distribution', []))
            }
        elif report_type == ReportType.REGISTRATION_TRENDS:
            return {
                'total_registrations': report_data.get('total_registrations', 0),
                'average_daily': report_data.get('average_daily', 0),
                'trend_months': len(report_data.get('monthly_trends', []))
            }
        elif report_type == ReportType.ID_CARD_STATUS:
            return {
                'total_cards': report_data.get('total_id_cards', 0),
                'coverage_percentage': report_data.get('coverage_percentage', 0),
                'expiring_cards': report_data.get('expiring_cards', 0)
            }
        return {}
    
    def _get_report_type_description(self, report_type: str) -> str:
        """Get description for report type"""
        descriptions = {
            ReportType.DEMOGRAPHIC: 'Analyze citizen demographics including age, gender, education, and location distribution',
            ReportType.REGISTRATION_TRENDS: 'Track registration patterns and trends over time',
            ReportType.ID_CARD_STATUS: 'Monitor ID card issuance, status distribution, and coverage',
            ReportType.SERVICE_ACCESS: 'Analyze how citizens access services using digital IDs',
            ReportType.MIGRATION_ANALYSIS: 'Track citizen transfers and migration patterns',
            ReportType.WORKFLOW_PERFORMANCE: 'Analyze workflow efficiency and processing times',
            ReportType.BIOMETRIC_QUALITY: 'Monitor biometric data quality and capture success rates',
            ReportType.COMPLIANCE_AUDIT: 'Generate compliance reports for auditing purposes',
            ReportType.CUSTOM: 'Create custom reports with specific parameters'
        }
        return descriptions.get(report_type, 'Custom report type')
    
    def _get_format_description(self, format_type: str) -> str:
        """Get description for format type"""
        descriptions = {
            ReportFormat.PDF: 'Professional PDF document suitable for printing and sharing',
            ReportFormat.EXCEL: 'Excel spreadsheet with multiple sheets and charts',
            ReportFormat.CSV: 'Comma-separated values file for data analysis',
            ReportFormat.JSON: 'JSON format for programmatic access and integration'
        }
        return descriptions.get(format_type, 'Unknown format')
    
    def _get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ReportScheduleViewSet(TenantAwareReportMixin, viewsets.ModelViewSet):
    """ViewSet for managing scheduled reports"""
    queryset = ReportSchedule.objects.all()
    serializer_class = ReportScheduleSerializer
    permission_classes = [SuperAdminOrTenantPermission]

    def get_queryset(self):
        tenant = self.get_tenant_context()

        if not tenant:
            return ReportSchedule.objects.none()

        with schema_context(tenant.schema_name):
            return ReportSchedule.objects.filter(created_by=self.request.user)

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
