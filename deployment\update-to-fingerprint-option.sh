#!/bin/bash

# Simple script to update to fingerprint option feature
echo "Updating GoID to Fingerprint Option Feature"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
DOCKER_HUB_USERNAME="aragawmebratu"
IMAGE_NAME="goid-production"
BACKEND_TAG="backend-fingerprint-option"
FRONTEND_TAG="frontend-fingerprint-option"

print_status "Configuration:"
print_status "  Backend: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
print_status "  Frontend: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"
echo ""

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found"
    print_error "Please run this script from the goid-deployment directory"
    exit 1
fi

print_success "Found docker-compose.yml"

# Backup current configuration
BACKUP_FILE="docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)"
print_status "Creating backup: $BACKUP_FILE"
cp docker-compose.yml "$BACKUP_FILE"

# Update docker-compose.yml to use new images
print_status "Updating docker-compose.yml with new images..."

# Update backend image
sed -i "s|image: aragawmebratu/goid-production:backend.*|image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG|g" docker-compose.yml

# Update frontend image
sed -i "s|image: aragawmebratu/goid-production:frontend.*|image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG|g" docker-compose.yml

print_success "Updated docker-compose.yml"

# Show what changed
print_status "Updated images:"
grep "image.*goid-production" docker-compose.yml

echo ""
read -p "Continue with deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Deployment cancelled"
    exit 0
fi

# Pull new images
print_status "Pulling new images..."
docker pull "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
docker pull "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"

# Stop services
print_status "Stopping services..."
docker-compose stop

# Run migrations
print_status "Running database migrations..."
docker-compose run --rm backend python manage.py migrate

# Start services
print_status "Starting services with new images..."
docker-compose up -d

# Wait for startup
print_status "Waiting for services to start..."
sleep 30

# Check status
print_status "Service status:"
docker-compose ps

# Test endpoints
print_status "Testing endpoints..."
echo "Frontend: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/ 2>/dev/null || echo 'Failed')"
echo "Backend: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:8000/admin/ 2>/dev/null || echo 'Failed')"

echo ""
print_success "Update completed!"
print_status ""
print_status "New Feature: Fingerprint Option"
print_status "  - Citizens can now be registered with or without fingerprints"
print_status "  - Option selection in biometric step"
print_status "  - Reason field for no-fingerprint cases"
print_status ""
print_status "Test at: http://************"
print_status "Go to citizen registration -> biometric step"
print_status ""
print_status "Backup saved as: $BACKUP_FILE"
