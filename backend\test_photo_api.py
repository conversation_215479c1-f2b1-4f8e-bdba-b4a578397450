#!/usr/bin/env python3
"""
Test script for photo processing API endpoints
Run this after starting the Django development server
"""

import requests
import base64
import io
from PIL import Image
import json

def create_test_image():
    """Create a test image for processing"""
    # Create a simple test image (200x200 with a person-like shape)
    image = Image.new('RGB', (200, 200), color='white')
    
    # Draw a simple person-like shape (circle for head, rectangle for body)
    from PIL import ImageDraw
    draw = ImageDraw.Draw(image)
    
    # Head (circle)
    draw.ellipse([75, 30, 125, 80], fill='brown', outline='black')
    
    # Body (rectangle)
    draw.rectangle([85, 80, 115, 150], fill='blue', outline='black')
    
    # Arms
    draw.rectangle([60, 90, 85, 100], fill='brown', outline='black')
    draw.rectangle([115, 90, 140, 100], fill='brown', outline='black')
    
    # Legs
    draw.rectangle([90, 150, 100, 180], fill='black', outline='black')
    draw.rectangle([100, 150, 110, 180], fill='black', outline='black')
    
    return image

def image_to_base64(image):
    """Convert PIL image to base64 string"""
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_bytes = buffer.getvalue()
    return base64.b64encode(img_bytes).decode('utf-8')

def test_process_photo_endpoint():
    """Test the process photo API endpoint"""
    print("=" * 60)
    print("TESTING PHOTO PROCESSING API ENDPOINT")
    print("=" * 60)
    
    # Create test image
    test_image = create_test_image()
    image_b64 = image_to_base64(test_image)
    
    # API endpoint
    url = "http://localhost:8000/api/idcards/process_photo/"
    
    # Test data
    data = {
        'image_data': f"data:image/png;base64,{image_b64}",
        'remove_background': True,
        'enhance': True,
        'style': 'professional'
    }
    
    try:
        print("📤 Sending request to:", url)
        print("   Remove background: True")
        print("   Enhance: True")
        print("   Style: professional")
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API call successful")
            print(f"   Success: {result.get('success', False)}")
            print(f"   Background removed: {result.get('background_removed', False)}")
            print(f"   Enhanced: {result.get('enhanced', False)}")
            print(f"   REMBG available: {result.get('rembg_available', False)}")
            print(f"   Format: {result.get('format', 'Unknown')}")
            
            if result.get('size'):
                print(f"   Output size: {result['size']}")
            
            if result.get('error'):
                print(f"   Error: {result['error']}")
            
            # Check if processed image is returned
            if result.get('processed_image'):
                print("   ✅ Processed image data returned")
                print(f"   Image data length: {len(result['processed_image'])} characters")
            else:
                print("   ❌ No processed image data returned")
                
            return result
            
        else:
            print(f"❌ API call failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Error: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Is the Django server running?")
        print("   Start server with: python manage.py runserver")
        return None
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def test_remove_background_endpoint():
    """Test the remove background API endpoint"""
    print("\n" + "=" * 60)
    print("TESTING BACKGROUND REMOVAL API ENDPOINT")
    print("=" * 60)
    
    # Create test image
    test_image = create_test_image()
    image_b64 = image_to_base64(test_image)
    
    # API endpoint
    url = "http://localhost:8000/api/idcards/remove_background/"
    
    # Test data
    data = {
        'image_data': f"data:image/png;base64,{image_b64}"
    }
    
    try:
        print("📤 Sending request to:", url)
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API call successful")
            print(f"   Success: {result.get('success', False)}")
            print(f"   Format: {result.get('format', 'Unknown')}")
            
            if result.get('size'):
                print(f"   Output size: {result['size']}")
            
            if result.get('error'):
                print(f"   Error: {result['error']}")
            
            # Check if processed image is returned
            if result.get('processed_image'):
                print("   ✅ Processed image data returned")
                print(f"   Image data length: {len(result['processed_image'])} characters")
            else:
                print("   ❌ No processed image data returned")
                
            return result
            
        else:
            print(f"❌ API call failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Error: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Is the Django server running?")
        return None
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def main():
    """Main test function"""
    print("GoID Photo Processing API Test")
    print("Make sure Django development server is running on localhost:8000")
    print()
    
    # Test process photo endpoint
    process_result = test_process_photo_endpoint()
    
    # Test remove background endpoint
    bg_result = test_remove_background_endpoint()
    
    print("\n" + "=" * 60)
    print("API TEST SUMMARY")
    print("=" * 60)
    
    if process_result and process_result.get('success'):
        print("✅ Photo processing API is working")
        if process_result.get('background_removed'):
            print("✅ Background removal is functional")
        else:
            print("⚠️  Background removal not working (using fallback)")
    else:
        print("❌ Photo processing API has issues")
    
    if bg_result and bg_result.get('success'):
        print("✅ Background removal API is working")
    else:
        print("❌ Background removal API has issues")

if __name__ == '__main__':
    main()
