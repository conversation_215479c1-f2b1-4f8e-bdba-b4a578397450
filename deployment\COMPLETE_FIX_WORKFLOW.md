# 🔧 Complete Fix Workflow for System Settings API

## Problem Summary
Multiple dashboard APIs are returning 404 errors because:
1. ✅ **Frontend fixed**: API calls now go through nginx (no more :8000)
2. ❌ **Backend issue**: Middleware doesn't treat dashboard endpoints as public

## Root Cause
The tenant middleware in `backend/tenants/middleware.py` has a `public_paths` list that **doesn't include** dashboard endpoints like:
- `/api/tenants/system-settings/`
- `/api/kebele-dashboard/`

So it tries to find a tenant context and fails with 404.

## Solution Applied
Added multiple dashboard endpoints to the `public_paths` list in the middleware so they bypass tenant processing:
- `/api/tenants/system-settings/` - System settings (uses public schema)
- `/api/kebele-dashboard/` - Kebele dashboard data
- `/api/dashboard-test/` - Dashboard testing endpoint

## Your Build-Push-Deploy Workflow

### Step 1: Build and Push Backend (Development Machine)
```bash
# Navigate to your project directory
cd /path/to/your/goid/project

# Build and push the fixed backend
chmod +x deployment/fix-and-push-backend.sh
bash deployment/fix-and-push-backend.sh
```

**This will:**
- ✅ Verify the middleware fix is applied
- ✅ Build new backend image: `aragawmebratu/goid-production:backend-middleware-fixed`
- ✅ Push to Docker Hub
- ✅ Also tag as `backend-latest`

### Step 2: Build and Push Frontend (Development Machine)
```bash
# Build and push the nginx-compatible frontend
chmod +x deployment/fix-and-push-frontend.sh
bash deployment/fix-and-push-frontend.sh
```

**This will:**
- ✅ Build frontend with API URL: `http://************` (no port)
- ✅ Push image: `aragawmebratu/goid-production:frontend-nginx-fixed`
- ✅ Also tag as `frontend-latest`

### Step 3: Deploy Backend on Server
```bash
# On your server (as root in goid-deployment folder)
cd /home/<USER>/goid-deployment

# Upload and run the backend update script
chmod +x update-backend-on-server.sh
bash update-backend-on-server.sh
```

**This will:**
- ✅ Update docker-compose.yml to use new backend image
- ✅ Pull `aragawmebratu/goid-production:backend-middleware-fixed`
- ✅ Restart backend service
- ✅ Test system settings API

### Step 4: Deploy Frontend on Server
```bash
# On your server (as root in goid-deployment folder)
cd /home/<USER>/goid-deployment

# Upload and run the frontend update script
chmod +x update-frontend-on-server.sh
bash update-frontend-on-server.sh
```

**This will:**
- ✅ Update docker-compose.yml to use new frontend image
- ✅ Pull `aragawmebratu/goid-production:frontend-nginx-fixed`
- ✅ Restart frontend service
- ✅ Test API connectivity

## Expected Results

### Before Fix:
```
Frontend → http://************/api/tenants/system-settings/
Nginx → backend:8000/api/tenants/system-settings/
Backend Middleware → 404 (no tenant found)
Browser Console → "Failed to load system settings"

Frontend → http://************/api/kebele-dashboard/
Nginx → backend:8000/api/kebele-dashboard/
Backend Middleware → 404 (no tenant found)
Browser Console → "Error fetching dashboard data"
```

### After Fix:
```
Frontend → http://************/api/tenants/system-settings/
Nginx → backend:8000/api/tenants/system-settings/
Backend Middleware → Public path detected, bypass tenant processing
SystemSettings Model → Return JSON data
Browser → System settings loaded successfully ✅

Frontend → http://************/api/kebele-dashboard/
Nginx → backend:8000/api/kebele-dashboard/
Backend Middleware → Public path detected, bypass tenant processing
Dashboard API → Return dashboard data
Browser → Kebele dashboard loads successfully ✅
```

## Files Changed

### Backend Changes:
```diff
# backend/tenants/middleware.py
- public_paths = ['/admin/', '/swagger/', '/redoc/', '/static/', '/media/', '/api/auth/', '/api/shared/', '/api/tenants/get-tenant-data/', '/api/idcards/services/public/']
+ public_paths = ['/admin/', '/swagger/', '/redoc/', '/static/', '/media/', '/api/auth/', '/api/shared/', '/api/tenants/get-tenant-data/', '/api/idcards/services/public/', '/api/tenants/system-settings/', '/api/kebele-dashboard/', '/api/dashboard-test/']
```

### Frontend Changes:
```diff
# frontend/public/config.js
- API_URL: 'http://************:8000'
+ API_URL: 'http://************'

# frontend/src/utils/axios.js
- return `http://${currentHost}:8000`;
+ return `http://${currentHost}`;
```

## Docker Hub Images

After running the build scripts, you'll have:
- 📦 `aragawmebratu/goid-production:backend-middleware-fixed`
- 📦 `aragawmebratu/goid-production:frontend-nginx-fixed`
- 📦 `aragawmebratu/goid-production:backend-latest` (updated)
- 📦 `aragawmebratu/goid-production:frontend-latest` (updated)

## Verification

After deployment, test these:

```bash
# System settings should return JSON (not 404)
curl http://************/api/tenants/system-settings/

# Frontend should load without console errors
curl http://************/

# Other APIs should still work
curl http://************/api/common/language/info/
```

## Rollback if Needed

If something goes wrong:

```bash
# On server, rollback to previous images
# Edit docker-compose.yml to use previous tags
# Or restore from backup:
cp docker-compose.yml.*.backup docker-compose.yml
docker-compose up -d
```

## Summary

This workflow follows your standard practice:
1. 🔧 **Fix code** on development machine
2. 🏗️ **Build images** with fixes
3. 📤 **Push to Docker Hub**
4. 📥 **Pull on server**
5. 🚀 **Deploy updated services**

The system settings API will work correctly after both backend and frontend updates are deployed!
