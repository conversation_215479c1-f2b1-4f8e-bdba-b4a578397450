from django.core.management.base import BaseCommand
from django.db import connection
from django_tenants.utils import schema_context
from tenants.models import Tenant

class Command(BaseCommand):
    help = 'Debug reports data aggregation'

    def handle(self, *args, **options):
        self.stdout.write("🔍 Debugging Reports Data Aggregation")
        self.stdout.write("=" * 50)
        
        # Get Zoble subcity (tenant ID 16)
        try:
            subcity_tenant = Tenant.objects.get(id=16)
            self.stdout.write(f"✅ Found subcity: {subcity_tenant.name} (ID: {subcity_tenant.id})")
            self.stdout.write(f"   Schema: {subcity_tenant.schema_name}")
            self.stdout.write(f"   Type: {subcity_tenant.type}")
        except Tenant.DoesNotExist:
            self.stdout.write("❌ Subcity tenant ID 16 not found")
            return
        
        # Get child kebeles
        child_kebeles = Tenant.objects.filter(parent=subcity_tenant, type='kebele')
        self.stdout.write(f"📍 Found {child_kebeles.count()} child kebeles:")
        
        total_citizens = 0
        
        for kebele in child_kebeles:
            self.stdout.write(f"\n🏘️  Processing kebele: {kebele.name} (ID: {kebele.id})")
            self.stdout.write(f"   Schema: {kebele.schema_name}")
            
            try:
                with schema_context(kebele.schema_name):
                    # Check current schema
                    with connection.cursor() as cursor:
                        cursor.execute("SELECT current_schema()")
                        current_schema = cursor.fetchone()[0]
                        self.stdout.write(f"   Current PostgreSQL schema: {current_schema}")
                    
                    # Import models inside schema context
                    from citizens.models import Citizen
                    from idcards.models import IDCard
                    
                    # Count citizens
                    citizen_count = Citizen.objects.count()
                    self.stdout.write(f"   👥 Citizens: {citizen_count}")
                    total_citizens += citizen_count
                    
                    # List citizens if any
                    if citizen_count > 0:
                        citizens = Citizen.objects.all()[:5]  # First 5 citizens
                        for citizen in citizens:
                            self.stdout.write(f"      - {citizen.first_name} {citizen.last_name} (ID: {citizen.id})")
                    
                    # Count ID cards
                    idcard_count = IDCard.objects.count()
                    self.stdout.write(f"   🆔 ID Cards: {idcard_count}")
                    
                    # Check ID card statuses
                    if idcard_count > 0:
                        from idcards.models import IDCardStatus
                        status_counts = {}
                        for status in IDCardStatus:
                            count = IDCard.objects.filter(status=status.value).count()
                            if count > 0:
                                status_counts[status.value] = count
                        self.stdout.write(f"      Status breakdown: {status_counts}")
                        
            except Exception as e:
                self.stdout.write(f"   ❌ Error processing kebele {kebele.name}: {str(e)}")
        
        self.stdout.write(f"\n📊 SUMMARY:")
        self.stdout.write(f"   Total citizens across all kebeles: {total_citizens}")
        self.stdout.write("=" * 50)
