#!/bin/bash

# Script to build and push fingerprint option feature
echo "🔧 Building and Pushing Fingerprint Option Feature"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
DOCKER_HUB_USERNAME="aragawmebratu"
IMAGE_NAME="goid-production"
BACKEND_TAG="backend-fingerprint-option"
FRONTEND_TAG="frontend-fingerprint-option"

print_status "Configuration:"
print_status "  Docker Hub: $DOCKER_HUB_USERNAME"
print_status "  Image: $IMAGE_NAME"
print_status "  Backend Tag: $BACKEND_TAG"
print_status "  Frontend Tag: $FRONTEND_TAG"
echo ""

# Check if we're in the right directory
if [ ! -f "backend/manage.py" ] || [ ! -f "frontend/package.json" ]; then
    print_error "backend/manage.py or frontend/package.json not found"
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_success "Found backend and frontend directories"

# Show the changes made
print_status "Fingerprint option feature changes:"
echo "===================================="
echo "📁 Backend changes:"
echo "  - Added fingerprint_option field to Citizen model"
echo "  - Added no_fingerprint_reason field to Citizen model"
echo "  - Updated serializers to include new fields"
echo "  - Created database migration"
echo ""
echo "📁 Frontend changes:"
echo "  - Updated BiometricCaptureStep with option selection"
echo "  - Added validation for fingerprint options"
echo "  - Added UI for 'with fingerprint' vs 'without fingerprint'"
echo "  - Added reason field for no fingerprint cases"
echo "===================================="
echo ""

# Ask for confirmation
read -p "🚀 Build and push both backend and frontend with fingerprint option feature? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Build cancelled"
    exit 0
fi

# Build Backend
print_status "Building backend Docker image..."
cd backend

docker build \
    -f Dockerfile.production \
    -t "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG" \
    .

if [ $? -eq 0 ]; then
    print_success "✅ Backend image built successfully"
else
    print_error "❌ Failed to build backend image"
    exit 1
fi

# Tag backend as latest
docker tag "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG" "$DOCKER_HUB_USERNAME/$IMAGE_NAME:backend-latest"
print_success "✅ Tagged backend image as backend-latest"

cd ..

# Build Frontend
print_status "Building frontend Docker image..."
cd frontend

docker build \
    -f Dockerfile.production \
    --build-arg VITE_API_URL="http://************" \
    --build-arg VITE_BIOMETRIC_SERVICE_URL="http://localhost:8002" \
    -t "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG" \
    .

if [ $? -eq 0 ]; then
    print_success "✅ Frontend image built successfully"
else
    print_error "❌ Failed to build frontend image"
    exit 1
fi

# Tag frontend as latest
docker tag "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG" "$DOCKER_HUB_USERNAME/$IMAGE_NAME:frontend-latest"
print_success "✅ Tagged frontend image as frontend-latest"

cd ..

# Push Backend Images
print_status "Pushing backend images to Docker Hub..."

docker push "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
if [ $? -eq 0 ]; then
    print_success "✅ Pushed $BACKEND_TAG"
else
    print_error "❌ Failed to push $BACKEND_TAG"
    exit 1
fi

docker push "$DOCKER_HUB_USERNAME/$IMAGE_NAME:backend-latest"
if [ $? -eq 0 ]; then
    print_success "✅ Pushed backend-latest"
else
    print_error "❌ Failed to push backend-latest"
    exit 1
fi

# Push Frontend Images
print_status "Pushing frontend images to Docker Hub..."

docker push "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"
if [ $? -eq 0 ]; then
    print_success "✅ Pushed $FRONTEND_TAG"
else
    print_error "❌ Failed to push $FRONTEND_TAG"
    exit 1
fi

docker push "$DOCKER_HUB_USERNAME/$IMAGE_NAME:frontend-latest"
if [ $? -eq 0 ]; then
    print_success "✅ Pushed frontend-latest"
else
    print_error "❌ Failed to push frontend-latest"
    exit 1
fi

print_success "🎉 All images built and pushed successfully!"
echo ""
print_status "📋 Summary of changes:"
print_status "  ✅ Added fingerprint option selection in registration"
print_status "  ✅ Citizens can now be registered with or without fingerprints"
print_status "  ✅ Added reason field for cases without fingerprints"
print_status "  ✅ Updated validation to handle both scenarios"
print_status "  ✅ Built and pushed Docker images with new feature"
echo ""
print_status "🚀 Next steps:"
print_status "  1. Update your docker-compose.yml to use the new images:"
print_status "     Backend: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
print_status "     Frontend: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"
print_status "  2. Run database migrations on the server"
print_status "  3. Restart the services with new images"
print_status "  4. Test the fingerprint option feature"
echo ""
print_status "🔗 Docker Hub images:"
print_status "  📦 $DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
print_status "  📦 $DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"
print_status "  📦 $DOCKER_HUB_USERNAME/$IMAGE_NAME:backend-latest"
print_status "  📦 $DOCKER_HUB_USERNAME/$IMAGE_NAME:frontend-latest"

echo ""
print_success "Fingerprint option feature is ready for deployment!"
print_status "Citizens can now be registered with or without fingerprints based on their individual needs."
