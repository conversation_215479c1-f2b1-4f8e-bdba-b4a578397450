version: '3.8'

services:
  backend:
    image: aragawmebratu/goid-production:backend-latest
    ports:
      - "8000:8000"
    environment:
      - DJANGO_SETTINGS_MODULE=goid.settings
      - DEBUG=False
      - DATABASE_URL=postgresql://goid_user:${DB_PASSWORD:-goid_password}@db:5432/goid_db
      - REDIS_URL=redis://redis:6379/0
      - ALLOWED_HOSTS=************,goid.uog.edu.et,localhost,127.0.0.1,*
      - SECRET_KEY=${SECRET_KEY:-change-this-secret-key}
      - CORS_ALLOWED_ORIGINS=http://************:3000,https://goid.uog.edu.et,http://goid.uog.edu.et:3000,http://localhost:3000,http://127.0.0.1:3000
      - CORS_ALLOW_ALL_ORIGINS=True
      - TZ=Africa/Addis_Ababa
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - backend_media:/app/media
      - backend_static:/app/static
      - ./backups:/app/backups
    networks:
      - goid_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    image: aragawmebratu/goid-production:frontend-latest
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://************:8000
      - REACT_APP_BIOMETRIC_SERVICE_URL=http://localhost:8002
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - goid_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: goid_db
      POSTGRES_USER: goid_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-goid_password}
      TZ: Africa/Addis_Ababa
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - goid_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U goid_user -d goid_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - goid_network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - db
    networks:
      - goid_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

volumes:
  postgres_data:
  redis_data:
  pgadmin_data:
  backend_media:
  backend_static:

networks:
  goid_network:
    driver: bridge
