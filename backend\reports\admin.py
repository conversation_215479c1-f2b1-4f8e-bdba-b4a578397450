from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Report, ReportTemplate, ReportSchedule, ReportAccess


@admin.register(ReportTemplate)
class ReportTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'report_type', 'created_by', 'is_active', 'is_system_template', 'created_at']
    list_filter = ['report_type', 'is_active', 'is_system_template', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'report_type', 'is_active', 'is_system_template')
        }),
        ('Configuration', {
            'fields': ('template_config', 'default_filters', 'chart_configs'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'report_type', 'format', 'status', 'generated_by', 
        'file_size_display', 'created_at', 'download_link'
    ]
    list_filter = ['report_type', 'format', 'status', 'created_at', 'generated_at']
    search_fields = ['title', 'description']
    readonly_fields = [
        'id', 'file_size', 'processing_time', 'total_records', 
        'created_at', 'updated_at', 'generated_at'
    ]
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (None, {
            'fields': ('title', 'description', 'report_type', 'template', 'format', 'status')
        }),
        ('Configuration', {
            'fields': ('filters', 'parameters', 'period_start', 'period_end'),
            'classes': ('collapse',)
        }),
        ('Generation Info', {
            'fields': (
                'generated_by', 'generated_at', 'processing_time', 
                'total_records', 'file_size', 'expires_at'
            )
        }),
        ('File & Results', {
            'fields': ('file', 'summary_data', 'error_message'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def file_size_display(self, obj):
        """Display file size in human readable format"""
        if obj.file_size:
            if obj.file_size < 1024:
                return f"{obj.file_size} B"
            elif obj.file_size < 1024 * 1024:
                return f"{obj.file_size / 1024:.1f} KB"
            else:
                return f"{obj.file_size / (1024 * 1024):.1f} MB"
        return "N/A"
    file_size_display.short_description = "File Size"
    
    def download_link(self, obj):
        """Display download link if available"""
        if obj.status == 'completed' and obj.file and not obj.is_expired:
            url = reverse('admin:reports_report_change', args=[obj.pk])
            return format_html(
                '<a href="{}" target="_blank">Download</a>',
                obj.file.url
            )
        return "N/A"
    download_link.short_description = "Download"


@admin.register(ReportSchedule)
class ReportScheduleAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'template', 'format', 'cron_expression', 
        'is_active', 'last_run', 'next_run', 'created_by'
    ]
    list_filter = ['format', 'is_active', 'created_at', 'last_run']
    search_fields = ['name', 'description']
    readonly_fields = ['id', 'last_run', 'next_run', 'created_at', 'updated_at']
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'template', 'format', 'is_active')
        }),
        ('Schedule', {
            'fields': ('cron_expression', 'timezone', 'last_run', 'next_run')
        }),
        ('Recipients', {
            'fields': ('email_recipients',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(ReportAccess)
class ReportAccessAdmin(admin.ModelAdmin):
    list_display = ['report', 'user', 'action', 'accessed_at', 'ip_address']
    list_filter = ['action', 'accessed_at']
    search_fields = ['report__title', 'user__username', 'ip_address']
    readonly_fields = ['id', 'accessed_at']
    date_hierarchy = 'accessed_at'
    
    def has_add_permission(self, request):
        """Disable manual creation of access logs"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Make access logs read-only"""
        return False
