from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from reports.models import ReportTemplate, ReportType

User = get_user_model()


class Command(BaseCommand):
    help = 'Create default report templates for the system'

    def handle(self, *args, **options):
        # Get or create a system user for templates
        system_user, created = User.objects.get_or_create(
            username='system',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'System',
                'last_name': 'User',
                'is_staff': True,
                'is_active': True
            }
        )

        templates = [
            {
                'name': 'Standard Demographic Report',
                'description': 'Comprehensive demographic analysis including age, gender, education, and location distribution',
                'report_type': ReportType.DEMOGRAPHIC,
                'template_config': {
                    'include_charts': True,
                    'chart_types': ['pie', 'bar'],
                    'sections': ['summary', 'gender', 'age_groups', 'education', 'employment', 'disability', 'location'],
                    'styling': {
                        'color_scheme': 'blue',
                        'font_family': 'Helvetica',
                        'include_logo': True
                    }
                },
                'default_filters': {},
                'chart_configs': [
                    {
                        'type': 'pie',
                        'data_source': 'gender_distribution',
                        'title': 'Gender Distribution',
                        'position': 'top'
                    },
                    {
                        'type': 'bar',
                        'data_source': 'age_group_distribution',
                        'title': 'Age Group Distribution',
                        'position': 'middle'
                    }
                ]
            },
            {
                'name': 'Registration Trends Analysis',
                'description': 'Track citizen registration patterns and trends over time periods',
                'report_type': ReportType.REGISTRATION_TRENDS,
                'template_config': {
                    'include_charts': True,
                    'chart_types': ['line', 'bar'],
                    'sections': ['summary', 'monthly_trends', 'daily_trends', 'hourly_distribution'],
                    'time_periods': ['daily', 'weekly', 'monthly'],
                    'styling': {
                        'color_scheme': 'green',
                        'font_family': 'Helvetica',
                        'include_logo': True
                    }
                },
                'default_filters': {
                    'period_days': 365
                },
                'chart_configs': [
                    {
                        'type': 'line',
                        'data_source': 'monthly_trends',
                        'title': 'Monthly Registration Trends',
                        'position': 'top'
                    },
                    {
                        'type': 'bar',
                        'data_source': 'daily_distribution',
                        'title': 'Registration by Day of Week',
                        'position': 'bottom'
                    }
                ]
            },
            {
                'name': 'ID Card Status Overview',
                'description': 'Monitor ID card issuance status, coverage, and workflow performance',
                'report_type': ReportType.ID_CARD_STATUS,
                'template_config': {
                    'include_charts': True,
                    'chart_types': ['pie', 'bar'],
                    'sections': ['summary', 'status_distribution', 'coverage_analysis', 'expiration_alerts'],
                    'alerts': {
                        'expiring_cards': True,
                        'expired_cards': True,
                        'low_coverage': True
                    },
                    'styling': {
                        'color_scheme': 'orange',
                        'font_family': 'Helvetica',
                        'include_logo': True
                    }
                },
                'default_filters': {},
                'chart_configs': [
                    {
                        'type': 'pie',
                        'data_source': 'status_distribution',
                        'title': 'ID Card Status Distribution',
                        'position': 'top'
                    },
                    {
                        'type': 'bar',
                        'data_source': 'monthly_creation',
                        'title': 'Monthly ID Card Creation',
                        'position': 'bottom'
                    }
                ]
            },
            {
                'name': 'Service Access Analytics',
                'description': 'Analyze how citizens access services using their digital IDs',
                'report_type': ReportType.SERVICE_ACCESS,
                'template_config': {
                    'include_charts': True,
                    'chart_types': ['bar', 'line'],
                    'sections': ['summary', 'service_usage', 'user_activity', 'access_trends'],
                    'metrics': ['total_interactions', 'unique_users', 'popular_services'],
                    'styling': {
                        'color_scheme': 'purple',
                        'font_family': 'Helvetica',
                        'include_logo': True
                    }
                },
                'default_filters': {
                    'period_days': 30
                },
                'chart_configs': [
                    {
                        'type': 'bar',
                        'data_source': 'service_usage_by_type',
                        'title': 'Service Usage by Type',
                        'position': 'top'
                    },
                    {
                        'type': 'line',
                        'data_source': 'daily_access_trends',
                        'title': 'Daily Access Trends',
                        'position': 'bottom'
                    }
                ]
            },
            {
                'name': 'Migration Analysis Report',
                'description': 'Track citizen transfers and migration patterns between kebeles',
                'report_type': ReportType.MIGRATION_ANALYSIS,
                'template_config': {
                    'include_charts': True,
                    'chart_types': ['bar', 'pie'],
                    'sections': ['summary', 'transfer_trends', 'source_destination_analysis', 'status_breakdown'],
                    'geographic_analysis': True,
                    'styling': {
                        'color_scheme': 'red',
                        'font_family': 'Helvetica',
                        'include_logo': True
                    }
                },
                'default_filters': {
                    'period_days': 90
                },
                'chart_configs': [
                    {
                        'type': 'pie',
                        'data_source': 'status_distribution',
                        'title': 'Transfer Status Distribution',
                        'position': 'top'
                    },
                    {
                        'type': 'bar',
                        'data_source': 'monthly_trends',
                        'title': 'Monthly Transfer Trends',
                        'position': 'bottom'
                    }
                ]
            }
        ]

        created_count = 0
        updated_count = 0

        for template_data in templates:
            template, created = ReportTemplate.objects.get_or_create(
                name=template_data['name'],
                report_type=template_data['report_type'],
                defaults={
                    'description': template_data['description'],
                    'template_config': template_data['template_config'],
                    'default_filters': template_data['default_filters'],
                    'chart_configs': template_data['chart_configs'],
                    'created_by': system_user,
                    'is_system_template': True,
                    'is_active': True
                }
            )

            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            else:
                # Update existing template
                template.description = template_data['description']
                template.template_config = template_data['template_config']
                template.default_filters = template_data['default_filters']
                template.chart_configs = template_data['chart_configs']
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated template: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'\nCompleted! Created {created_count} new templates, updated {updated_count} existing templates.'
            )
        )
