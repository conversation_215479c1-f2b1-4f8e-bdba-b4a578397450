#!/bin/bash

# GoID Nginx Deployment Script
echo "🚀 GoID Nginx Deployment Script"
echo "==============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root or with sudo
if [ "$EUID" -eq 0 ]; then
    print_warning "Running as root. This is not recommended for production."
fi

# Stop existing services
print_status "Stopping existing services..."
docker-compose down 2>/dev/null || true

# Pull latest images
print_status "Pulling latest Docker images..."
docker pull aragawmebratu/goid-production:backend-latest
docker pull aragawmebratu/goid-production:frontend-latest
docker pull postgres:15
docker pull redis:7-alpine
docker pull dpage/pgadmin4:latest
docker pull nginx:alpine

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p backups
mkdir -p logs

# Backup current docker-compose.yml
if [ -f docker-compose.yml ]; then
    print_status "Backing up current docker-compose.yml..."
    cp docker-compose.yml docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)
fi

# Copy the nginx docker-compose file
print_status "Using nginx Docker Compose configuration..."
cp docker-compose.nginx.yml docker-compose.yml

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    print_status "Creating environment file..."
    cat > .env << EOF
# GoID Production Environment Configuration

# Database Configuration
DB_PASSWORD=goid_password_change_in_production

# Security Configuration
SECRET_KEY=change-this-secret-key-to-something-very-secure-in-production

# PgAdmin Configuration
PGADMIN_PASSWORD=admin123_change_in_production

# Network Configuration
SERVER_IP=************
DOMAIN=goid.uog.edu.et
EOF
    print_warning "Please edit .env file to set your production passwords and secrets!"
fi

# Start services
print_status "Starting services with nginx reverse proxy..."
docker-compose up -d

# Wait for services to start
print_status "Waiting for services to start..."
sleep 30

# Check service status
print_status "Checking service status..."
docker-compose ps

# Test nginx connectivity
print_status "Testing nginx connectivity..."
for i in {1..10}; do
    if curl -f http://localhost/health 2>/dev/null; then
        print_success "Nginx is responding!"
        break
    else
        print_status "Attempt $i/10: Nginx not ready yet, waiting..."
        sleep 10
    fi
done

# Test backend API through nginx
print_status "Testing backend API through nginx..."
for i in {1..10}; do
    if curl -f http://localhost/api/common/language/info/ 2>/dev/null; then
        print_success "Backend API is accessible through nginx!"
        break
    else
        print_status "Attempt $i/10: Backend API not ready yet, waiting..."
        sleep 10
    fi
done

# Test frontend through nginx
print_status "Testing frontend through nginx..."
if curl -f http://localhost/ 2>/dev/null; then
    print_success "Frontend is accessible through nginx!"
else
    print_warning "Frontend not accessible through nginx"
fi

# Get server IP
SERVER_IP=$(hostname -I | awk '{print $1}')

# Show final status
print_status "Nginx deployment complete!"
print_status ""
print_status "🌐 Services are now available at:"
print_status "  Main Application: http://$SERVER_IP"
print_status "  Frontend: http://$SERVER_IP"
print_status "  Backend API: http://$SERVER_IP/api/"
print_status "  Django Admin: http://$SERVER_IP/admin/"
print_status "  API Documentation: http://$SERVER_IP/swagger/"
print_status "  PgAdmin: http://$SERVER_IP:5050"
print_status ""
print_status "🔧 Architecture:"
print_status "  - Nginx (Port 80) → Frontend & Backend"
print_status "  - Frontend (Internal Port 80)"
print_status "  - Backend (Internal Port 8000)"
print_status "  - Database (Internal Port 5432)"
print_status "  - Redis (Internal Port 6379)"
print_status ""
print_status "📋 Next Steps:"
print_status "  1. Test the application: http://$SERVER_IP"
print_status "  2. Update DNS to point goid.uog.edu.et to $SERVER_IP"
print_status "  3. Set up SSL certificates for HTTPS"
print_status "  4. Configure firewall to allow port 80 and 443"
print_status ""
print_status "🆘 Troubleshooting:"
print_status "  - View logs: docker-compose logs -f [service_name]"
print_status "  - Check status: docker-compose ps"
print_status "  - Restart services: docker-compose restart"

print_success "Deployment completed successfully!"
