#!/bin/bash

# Script to add nginx to existing docker-compose.yml in goid-deployment folder
echo "🚀 Adding Nginx to GoID Docker Compose"
echo "======================================"

# Ensure we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found"
    echo "Current directory: $(pwd)"
    echo "Files here:"
    ls -la
    echo ""
    echo "Please navigate to goid-deployment folder first:"
    echo "cd /home/<USER>/goid-deployment"
    exit 1
fi

echo "✅ Found docker-compose.yml in $(pwd)"

# Backup current file
BACKUP_FILE="docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)"
echo "📁 Creating backup: $BACKUP_FILE"
cp docker-compose.yml "$BACKUP_FILE"

# Check if nginx already exists
if grep -q "nginx:" docker-compose.yml; then
    echo "⚠️  Nginx service already exists"
    echo "Current services:"
    docker-compose ps 2>/dev/null || echo "Docker compose not running"
    exit 0
fi

# Show current services
echo ""
echo "📋 Current services in docker-compose.yml:"
grep -E "^\s+[a-zA-Z].*:" docker-compose.yml | sed 's/://g' | sed 's/^/  - /'
echo ""

# Create nginx.conf
echo "📝 Creating nginx.conf..."
cat > nginx.conf << 'EOF'
upstream backend {
    server backend:8000;
}

upstream frontend {
    server frontend:80;
}

server {
    listen 80;
    server_name ************ goid.uog.edu.et localhost;
    
    client_max_body_size 100M;
    
    # Backend API routes
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin "http://************" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "http://************" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }
    
    # Django admin routes
    location /admin/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Django static and media files
    location /static/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
    }
    
    location /media/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
    }
    
    # Frontend routes (catch-all)
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Health check
    location /health {
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

echo "✅ Created nginx.conf"

# Add nginx service to docker-compose.yml
echo "🔧 Adding nginx service to docker-compose.yml..."

# Create a temporary file with nginx service
cat > /tmp/nginx_service.txt << 'EOF'
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - backend
      - frontend
    networks:
      - goid_network
    restart: unless-stopped

EOF

# Insert nginx service before volumes section
if grep -q "^volumes:" docker-compose.yml; then
    # Insert before volumes section
    sed -i '/^volumes:/i\
  nginx:\
    image: nginx:alpine\
    ports:\
      - "80:80"\
    volumes:\
      - ./nginx.conf:/etc/nginx/conf.d/default.conf\
    depends_on:\
      - backend\
      - frontend\
    networks:\
      - goid_network\
    restart: unless-stopped\
' docker-compose.yml
else
    # Append at the end if no volumes section
    cat /tmp/nginx_service.txt >> docker-compose.yml
fi

# Modify frontend to use expose instead of ports (to avoid port conflicts)
sed -i '/frontend:/,/^[[:space:]]*[^[:space:]]/ s/ports:/expose:/' docker-compose.yml
sed -i 's/- "3000:80"/- "80"/' docker-compose.yml

echo "✅ Modified docker-compose.yml with nginx service"

# Show modified content
echo ""
echo "📋 Modified docker-compose.yml:"
echo "==============================="
cat docker-compose.yml
echo "==============================="
echo ""

# Ask for confirmation
echo ""
read -p "🚀 Deploy nginx changes now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 Deploying nginx configuration..."

    # Stop current services
    echo "⏹️  Stopping current services..."
    docker-compose down

    # Pull nginx image
    echo "📥 Pulling nginx image..."
    docker pull nginx:alpine

    # Start with new configuration
    echo "▶️  Starting services with nginx..."
    docker-compose up -d

    # Wait for services
    echo "⏳ Waiting for services to start (30 seconds)..."
    sleep 30

    # Show status
    echo ""
    echo "📊 Service status:"
    docker-compose ps

    # Test connectivity
    echo ""
    echo "🧪 Testing connectivity..."
    echo "  Port 80: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:80 2>/dev/null || echo 'Connection refused')"
    echo "  API endpoint: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/api/common/language/info/ 2>/dev/null || echo 'Failed')"
    echo "  Frontend: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/ 2>/dev/null || echo 'Failed')"
    echo "  Admin panel: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/admin/ 2>/dev/null || echo 'Failed')"

    # Test external access
    echo "  External IP: $(curl -s -o /dev/null -w '%{http_code}' http://************/ 2>/dev/null || echo 'Failed')"

    echo ""
    echo "🎉 Nginx deployment completed!"
    echo ""
    echo "📍 Your application is now available at:"
    echo "  🌐 Main Application: http://************"
    echo "  🔌 Backend API: http://************/api/"
    echo "  ⚙️  Django Admin: http://************/admin/"
    echo "  📊 PgAdmin: http://************:5050"
    echo ""
    echo "✅ This should fix all the API access issues!"
    echo "💾 Original config backed up as: $BACKUP_FILE"

else
    echo "❌ Deployment cancelled"
    echo "💾 Configuration ready, backup saved as: $BACKUP_FILE"
    echo "🔧 To deploy later, run: docker-compose down && docker-compose up -d"
fi

echo ""
echo "📋 Summary:"
echo "  - Added nginx reverse proxy on port 80"
echo "  - Frontend now accessible via nginx (no port 3000)"
echo "  - Backend API routed through nginx (fixes tenant issues)"
echo "  - All services communicate internally"
echo "  - Professional URLs without port numbers"
