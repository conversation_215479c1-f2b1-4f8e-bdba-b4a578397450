#!/bin/bash

# Build and Push GoID Docker Images with Fingerprint Next Button Fix
# This script builds both frontend and backend images and pushes them to Docker Hub

set -e  # Exit on any error

echo "🚀 Building and Pushing GoID Docker Images with Fingerprint Fix..."

# Configuration
DOCKER_USERNAME="aragawmebratu"
FRONTEND_IMAGE="$DOCKER_USERNAME/goid-production:frontend-fingerprint-fix"
BACKEND_IMAGE="$DOCKER_USERNAME/goid-production:backend-fingerprint-fix"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running ✅"

# Check if we're logged into Docker Hub
if ! docker info | grep -q "Username:"; then
    print_warning "Not logged into Docker Hub. Please run: docker login"
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Build Frontend Image
print_status "Building Frontend Image..."
echo "📦 Building: $FRONTEND_IMAGE"

cd frontend
if docker build -f Dockerfile.production -t "$FRONTEND_IMAGE" .; then
    print_success "Frontend image built successfully"
else
    print_error "Failed to build frontend image"
    cd ..
    exit 1
fi
cd ..

# Build Backend Image
print_status "Building Backend Image..."
echo "📦 Building: $BACKEND_IMAGE"

cd backend
if docker build -f Dockerfile.production -t "$BACKEND_IMAGE" .; then
    print_success "Backend image built successfully"
else
    print_error "Failed to build backend image"
    cd ..
    exit 1
fi
cd ..

# Push Frontend Image
print_status "Pushing Frontend Image to Docker Hub..."
echo "🚀 Pushing: $FRONTEND_IMAGE"

if docker push "$FRONTEND_IMAGE"; then
    print_success "Frontend image pushed successfully"
else
    print_error "Failed to push frontend image"
    exit 1
fi

# Push Backend Image
print_status "Pushing Backend Image to Docker Hub..."
echo "🚀 Pushing: $BACKEND_IMAGE"

if docker push "$BACKEND_IMAGE"; then
    print_success "Backend image pushed successfully"
else
    print_error "Failed to push backend image"
    exit 1
fi

# Summary
echo ""
echo "🎉 Build and Push Complete!"
echo "================================"
echo "Frontend Image: $FRONTEND_IMAGE"
echo "Backend Image:  $BACKEND_IMAGE"
echo ""
echo "📋 Next Steps:"
echo "1. SSH to your server"
echo "2. Update docker-compose.yml to use the new images:"
echo "   - frontend: $FRONTEND_IMAGE"
echo "   - backend:  $BACKEND_IMAGE"
echo "3. Run: docker-compose pull && docker-compose up -d"
echo ""
echo "🔧 Changes in this build:"
echo "- Fixed fingerprint registration 'Next' button issue"
echo "- Improved validation logic for 'without fingerprint' option"
echo "- Enhanced real-time validation feedback"
echo "- Better error messages for validation failures"
