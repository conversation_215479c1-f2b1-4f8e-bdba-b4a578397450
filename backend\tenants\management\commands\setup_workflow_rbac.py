"""
Management command to set up comprehensive RBAC system with workflow switching support.

This command creates predefined roles and permissions for all tenant types and
enables workflow switching between autonomous and centralized modes.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from tenants.models import Tenant, TenantWorkflowConfig
from tenants.models_workflow import WORKFLOW_TEMPLATES
from users.models import User
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Set up comprehensive RBAC system with workflow switching support'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=int,
            help='Specific tenant ID to set up (optional, will set up all if not specified)'
        )
        parser.add_argument(
            '--workflow-type',
            choices=['autonomous', 'centralized'],
            default='centralized',
            help='Default workflow type for kebeles'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing groups and permissions'
        )

    def handle(self, *args, **options):
        tenant_id = options.get('tenant_id')
        workflow_type = options.get('workflow_type')
        force = options.get('force')

        self.stdout.write(
            self.style.SUCCESS('🚀 Setting up Comprehensive RBAC System with Workflow Switching')
        )

        try:
            with transaction.atomic():
                if tenant_id:
                    tenant = Tenant.objects.get(id=tenant_id)
                    self.setup_tenant_rbac(tenant, workflow_type, force)
                else:
                    self.setup_all_tenants_rbac(workflow_type, force)

                self.stdout.write(
                    self.style.SUCCESS('✅ RBAC system setup completed successfully!')
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error setting up RBAC system: {str(e)}')
            )
            raise

    def setup_all_tenants_rbac(self, default_workflow_type, force):
        """Set up RBAC for all tenants."""
        tenants = Tenant.objects.all()
        
        for tenant in tenants:
            self.stdout.write(f'📋 Setting up RBAC for {tenant.name} ({tenant.type})')
            
            # Determine workflow type based on tenant type
            if tenant.type == 'kebele':
                workflow_type = default_workflow_type
            else:
                workflow_type = 'centralized'  # Subcity and city always use centralized
            
            self.setup_tenant_rbac(tenant, workflow_type, force)

    def setup_tenant_rbac(self, tenant, workflow_type, force):
        """Set up RBAC for a specific tenant."""
        
        # Create or update workflow configuration
        workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
            tenant=tenant,
            defaults={'workflow_type': workflow_type}
        )
        
        if not created and workflow_config.workflow_type != workflow_type:
            workflow_config.workflow_type = workflow_type
            workflow_config.save()
            self.stdout.write(f'  📝 Updated workflow type to {workflow_type}')

        # Get appropriate permissions template
        template = self.get_permissions_template(tenant.type, workflow_type)
        
        # Create groups and assign permissions
        for role_name, permissions in template.items():
            self.create_role_group(tenant, role_name, permissions, force)

    def get_permissions_template(self, tenant_type, workflow_type):
        """Get the appropriate permissions template based on tenant type and workflow."""
        
        if tenant_type == 'kebele':
            if workflow_type == 'autonomous':
                return {
                    'clerk': [
                        'register_citizens',
                        'view_citizens_list',
                        'view_citizen_details',
                        'generate_id_cards',
                        'view_id_cards_list',
                        'view_kebele_dashboard',
                        'send_id_card_for_approval'
                    ],
                    'kebele_leader': [
                        'view_own_kebele_data',
                        'view_kebele_dashboard',
                        'view_kebele_reports',
                        'view_citizens_list',
                        'view_citizen_details',
                        'view_id_cards_list',
                        'approve_id_cards',
                        'verify_documents',
                        'create_transfers',
                        'approve_transfer_requests',
                        'create_clearances',
                        'view_clearances'
                    ],
                    'print_id_cards': [
                        'print_id_cards'
                    ]
                }
            else:  # centralized
                return {
                    'clerk': [
                        'register_citizens',
                        'view_citizens_list',
                        'view_citizen_details',
                        'generate_id_cards',
                        'view_id_cards_list',
                        'view_kebele_dashboard',
                        'send_id_card_for_approval'
                    ],
                    'kebele_leader': [
                        'view_own_kebele_data',
                        'view_kebele_dashboard',
                        'view_kebele_reports',
                        'view_citizens_list',
                        'view_citizen_details',
                        'view_id_cards_list',
                        'approve_id_cards',
                        'verify_documents',
                        'create_transfers',
                        'approve_transfer_requests',
                        'create_clearances',
                        'view_clearances'
                    ]
                }
        
        elif tenant_type == 'subcity':
            return {
                'subcity_admin': [
                    'view_child_kebeles_data',
                    'view_subcity_dashboard',
                    'view_subcity_reports',
                    'create_kebele_users'
                ],
                'subcity_system_admin': [
                    'create_kebele_users'
                ],
                'print_id_cards': [
                    'print_id_cards'
                ]
            }
        
        elif tenant_type == 'city':
            return {
                'city_admin': [
                    'view_child_subcities_data',
                    'view_city_dashboard',
                    'view_city_reports',
                    'manage_tenants',
                    'view_all_reports',
                    'create_subcity_users'
                ],
                'city_system_admin': [
                    'create_subcity_users'
                ]
            }
        
        return {}

    def create_role_group(self, tenant, role_name, permissions, force):
        """Create a role group with specific permissions for a tenant."""
        
        group_name = f"{tenant.schema_name}_{role_name}"
        
        # Check if group exists
        group, created = Group.objects.get_or_create(name=group_name)
        
        if created:
            self.stdout.write(f'  ✅ Created group: {group_name}')
        elif force:
            group.permissions.clear()
            self.stdout.write(f'  🔄 Cleared existing permissions for: {group_name}')
        else:
            self.stdout.write(f'  ⏭️  Group already exists: {group_name}')
            return

        # Add permissions to group
        for permission_name in permissions:
            try:
                # Try to find existing permission or create a custom one
                permission = self.get_or_create_permission(permission_name)
                group.permissions.add(permission)
                self.stdout.write(f'    📋 Added permission: {permission_name}')
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'    ⚠️  Could not add permission {permission_name}: {str(e)}')
                )

    def get_or_create_permission(self, permission_name):
        """Get or create a permission."""
        
        # Try to find existing permission
        try:
            return Permission.objects.get(codename=permission_name)
        except Permission.DoesNotExist:
            pass
        
        # Create custom permission
        content_type = ContentType.objects.get_for_model(User)
        permission, created = Permission.objects.get_or_create(
            codename=permission_name,
            defaults={
                'name': permission_name.replace('_', ' ').title(),
                'content_type': content_type
            }
        )
        
        if created:
            self.stdout.write(f'    🆕 Created new permission: {permission_name}')
        
        return permission
