@echo off
echo ========================================
echo   GoID Docker Build and Test Script
echo ========================================
echo.

REM Check if Docker is available
echo Checking Docker availability...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not available or not running
    echo Please install Docker Desktop and ensure it's running
    pause
    exit /b 1
)
echo ✅ Docker is available

REM Check if Docker daemon is running
echo Checking Docker daemon...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker daemon is not running
    echo Please start Docker Desktop
    pause
    exit /b 1
)
echo ✅ Docker daemon is running

echo.
echo ========================================
echo   Building Backend Docker Image
echo ========================================

REM Generate timestamp for version
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%-%HH%%Min%%Sec%"

set "BACKEND_IMAGE=aragawmebratu/goid-production:backend-%timestamp%"
set "BACKEND_LATEST=aragawmebratu/goid-production:backend-latest"

echo Building backend image: %BACKEND_IMAGE%
echo.

REM Build backend image
cd backend
docker build -f Dockerfile.production -t %BACKEND_IMAGE% -t %BACKEND_LATEST% .
if %errorlevel% neq 0 (
    echo ❌ Backend build failed
    pause
    exit /b 1
)
echo ✅ Backend image built successfully
cd ..

echo.
echo ========================================
echo   Testing Photo Processing in Container
echo ========================================

REM Create a temporary container to test photo processing
echo Creating test container...
docker run --rm -d --name goid-test-backend %BACKEND_IMAGE% tail -f /dev/null
if %errorlevel% neq 0 (
    echo ❌ Failed to create test container
    pause
    exit /b 1
)

echo Testing rembg availability...
docker exec goid-test-backend python -c "
try:
    from rembg import remove, new_session
    print('✅ rembg imported successfully')
    session = new_session('u2net')
    print('✅ u2net session created successfully')
    print('✅ Background removal should work')
except ImportError as e:
    print(f'❌ rembg import failed: {e}')
except Exception as e:
    print(f'❌ rembg session creation failed: {e}')
"

echo Testing PhotoProcessor class...
docker exec goid-test-backend python -c "
import sys
sys.path.append('/app')
try:
    from idcards.photo_processing import PhotoProcessor, REMBG_AVAILABLE, CV2_AVAILABLE
    print(f'✅ PhotoProcessor imported successfully')
    print(f'   REMBG_AVAILABLE: {REMBG_AVAILABLE}')
    print(f'   CV2_AVAILABLE: {CV2_AVAILABLE}')
    processor = PhotoProcessor()
    print(f'   Session initialized: {processor.session is not None}')
except Exception as e:
    print(f'❌ PhotoProcessor test failed: {e}')
"

echo Testing with sample image...
docker exec goid-test-backend python -c "
import sys
sys.path.append('/app')
try:
    from PIL import Image
    import io
    from idcards.photo_processing import PhotoProcessor
    
    # Create test image
    test_image = Image.new('RGB', (100, 100), color='red')
    img_buffer = io.BytesIO()
    test_image.save(img_buffer, format='PNG')
    img_bytes = img_buffer.getvalue()
    
    # Test processing
    processor = PhotoProcessor()
    result = processor.process_id_photo(img_bytes, remove_bg=True, enhance=True)
    
    print(f'✅ Photo processing test completed')
    print(f'   Success: {result.get(\"success\", False)}')
    print(f'   Background removed: {result.get(\"background_removed\", False)}')
    print(f'   REMBG available: {result.get(\"rembg_available\", False)}')
    
    if result.get('error'):
        print(f'   Error: {result[\"error\"]}')
        
except Exception as e:
    print(f'❌ Sample image test failed: {e}')
"

REM Clean up test container
echo Cleaning up test container...
docker stop goid-test-backend >nul 2>&1

echo.
echo ========================================
echo   Build Summary
echo ========================================
echo ✅ Backend image: %BACKEND_IMAGE%
echo ✅ Latest tag: %BACKEND_LATEST%
echo.

REM Ask if user wants to push to Docker Hub
set /p push_choice="Push images to Docker Hub? (y/N): "
if /i "%push_choice%"=="y" (
    echo.
    echo ========================================
    echo   Pushing to Docker Hub
    echo ========================================
    
    echo Pushing %BACKEND_IMAGE%...
    docker push %BACKEND_IMAGE%
    if %errorlevel% neq 0 (
        echo ❌ Failed to push versioned image
    ) else (
        echo ✅ Versioned image pushed successfully
    )
    
    echo Pushing %BACKEND_LATEST%...
    docker push %BACKEND_LATEST%
    if %errorlevel% neq 0 (
        echo ❌ Failed to push latest image
    ) else (
        echo ✅ Latest image pushed successfully
    )
) else (
    echo ⏭️  Skipping Docker Hub push
)

echo.
echo ========================================
echo   Deployment Commands
echo ========================================
echo Copy these commands to deploy on your production server:
echo.
echo # Pull new image:
echo docker pull %BACKEND_IMAGE%
echo.
echo # Update docker-compose and restart:
echo set BACKEND_VERSION=%timestamp%
echo docker-compose -f docker-compose.production.yml up -d
echo.
echo # Or update your docker-compose.production.yml file with:
echo #   image: %BACKEND_IMAGE%
echo # Then run: docker-compose -f docker-compose.production.yml up -d
echo.

echo ========================================
echo   Build Completed Successfully!
echo ========================================
pause
