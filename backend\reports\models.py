from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.validators import FileExtensionValidator
import uuid


class ReportType(models.TextChoices):
    """Types of reports available in the system"""
    DEMOGRAPHIC = 'demographic', 'Demographic Analysis'
    REGISTRATION_TRENDS = 'registration_trends', 'Registration Trends'
    ID_CARD_STATUS = 'id_card_status', 'ID Card Status Report'
    SERVICE_ACCESS = 'service_access', 'Service Access Analytics'
    MIGRATION_ANALYSIS = 'migration_analysis', 'Migration Analysis'
    WORKFLOW_PERFORMANCE = 'workflow_performance', 'Workflow Performance'
    BIOMETRIC_QUALITY = 'biometric_quality', 'Biometric Quality Report'
    COMPLIANCE_AUDIT = 'compliance_audit', 'Compliance Audit'
    CUSTOM = 'custom', 'Custom Report'


class ReportFormat(models.TextChoices):
    """Available export formats for reports"""
    PDF = 'pdf', 'PDF Document'
    EXCEL = 'excel', 'Excel Spreadsheet'
    CSV = 'csv', 'CSV File'
    JSON = 'json', 'JSON Data'


class ReportStatus(models.TextChoices):
    """Status of report generation"""
    PENDING = 'pending', 'Pending'
    PROCESSING = 'processing', 'Processing'
    COMPLETED = 'completed', 'Completed'
    FAILED = 'failed', 'Failed'
    EXPIRED = 'expired', 'Expired'


class ReportTemplate(models.Model):
    """Templates for different types of reports"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, default='Untitled Report Template')
    description = models.TextField(blank=True, default='')
    report_type = models.CharField(max_length=50, choices=ReportType.choices, default=ReportType.DEMOGRAPHIC)
    
    # Template configuration
    template_config = models.JSONField(default=dict, help_text="Configuration for report generation")
    default_filters = models.JSONField(default=dict, help_text="Default filters for this template")
    chart_configs = models.JSONField(default=list, help_text="Chart configurations")
    
    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_templates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    is_system_template = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['name']
        
    def __str__(self):
        return f"{self.name} ({self.get_report_type_display()})"


class Report(models.Model):
    """Generated reports with metadata and file storage"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=300, default='Untitled Report')
    description = models.TextField(blank=True, default='')
    
    # Report configuration
    report_type = models.CharField(max_length=50, choices=ReportType.choices)
    template = models.ForeignKey(ReportTemplate, on_delete=models.SET_NULL, null=True, blank=True)
    format = models.CharField(max_length=20, choices=ReportFormat.choices)
    
    # Filters and parameters used for generation
    filters = models.JSONField(default=dict, help_text="Filters applied during report generation")
    parameters = models.JSONField(default=dict, help_text="Additional parameters for report generation")
    
    # Time period for the report
    period_start = models.DateTimeField(null=True, blank=True)
    period_end = models.DateTimeField(null=True, blank=True)
    
    # Generation metadata
    status = models.CharField(max_length=20, choices=ReportStatus.choices, default=ReportStatus.PENDING)
    generated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='generated_reports')
    generated_at = models.DateTimeField(null=True, blank=True)
    
    # File storage
    file = models.FileField(
        upload_to='reports/%Y/%m/',
        null=True,
        blank=True,
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'xlsx', 'csv', 'json'])]
    )
    file_size = models.PositiveIntegerField(null=True, blank=True, help_text="File size in bytes")
    
    # Processing information
    processing_time = models.DurationField(null=True, blank=True)
    error_message = models.TextField(blank=True, default='')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    # Statistics captured at generation time
    total_records = models.PositiveIntegerField(null=True, blank=True)
    summary_data = models.JSONField(default=dict, help_text="Summary statistics for quick access")
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['report_type', 'status']),
            models.Index(fields=['generated_by', 'created_at']),
            models.Index(fields=['period_start', 'period_end']),
        ]
        
    def __str__(self):
        return f"{self.title} ({self.get_format_display()})"
    
    @property
    def is_expired(self):
        """Check if the report has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False
    
    def mark_as_completed(self, file_path=None, processing_time=None):
        """Mark report as completed with optional file and timing info"""
        self.status = ReportStatus.COMPLETED
        self.generated_at = timezone.now()
        if file_path:
            self.file = file_path
        if processing_time:
            self.processing_time = processing_time
        self.save()
    
    def mark_as_failed(self, error_message):
        """Mark report as failed with error message"""
        self.status = ReportStatus.FAILED
        self.error_message = error_message
        self.save()


class ReportSchedule(models.Model):
    """Scheduled report generation"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, default='Untitled Schedule')
    description = models.TextField(blank=True, default='')
    
    # Schedule configuration
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE)
    format = models.CharField(max_length=20, choices=ReportFormat.choices, default=ReportFormat.PDF)

    # Scheduling
    cron_expression = models.CharField(max_length=100, help_text="Cron expression for scheduling", default='0 0 * * *')
    timezone = models.CharField(max_length=50, default='UTC')
    
    # Recipients
    email_recipients = models.JSONField(default=list, help_text="List of email addresses to send reports to")
    
    # Status
    is_active = models.BooleanField(default=True)
    last_run = models.DateTimeField(null=True, blank=True)
    next_run = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        
    def __str__(self):
        return f"{self.name} - {self.cron_expression}"


class ReportAccess(models.Model):
    """Track who has accessed which reports"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    report = models.ForeignKey(Report, on_delete=models.CASCADE, related_name='access_logs')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    
    # Access details
    accessed_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True, default='')

    # Action performed
    action = models.CharField(max_length=50, choices=[
        ('view', 'Viewed'),
        ('download', 'Downloaded'),
        ('share', 'Shared'),
    ], default='view')
    
    class Meta:
        ordering = ['-accessed_at']
        indexes = [
            models.Index(fields=['report', 'user']),
            models.Index(fields=['accessed_at']),
        ]
        
    def __str__(self):
        return f"{self.user.username} {self.action} {self.report.title}"
