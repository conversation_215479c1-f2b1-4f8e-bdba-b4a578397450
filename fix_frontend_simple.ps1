# GoID Frontend Fix Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  GoID Photo Processing Frontend Fix" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Issue Found:" -ForegroundColor Yellow
Write-Host "   Frontend was calling: /api/idcards/process-photo/ (with hyphen)" -ForegroundColor Gray
Write-Host "   Backend expects:      /api/idcards/process_photo/ (with underscore)" -ForegroundColor Gray
Write-Host ""

Write-Host "Fix Applied:" -ForegroundColor Green
Write-Host "   Updated frontend URL to use underscore: process_photo" -ForegroundColor Gray
Write-Host ""

# Check if Docker is available
Write-Host "Checking Docker availability..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Docker is available: $dockerVersion" -ForegroundColor Green
    } else {
        throw "Docker command failed"
    }
} catch {
    Write-Host "Docker is not available or not running" -ForegroundColor Red
    Write-Host "Please install Docker Desktop and ensure it is running" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Building Fixed Frontend Image" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Generate timestamp for version
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$frontendImage = "aragawmebratu/goid-production:frontend-$timestamp"
$frontendLatest = "aragawmebratu/goid-production:frontend-latest"

Write-Host "Building frontend image: $frontendImage" -ForegroundColor Yellow
Write-Host ""

# Build frontend image
Set-Location frontend
try {
    Write-Host "Building frontend Docker image..." -ForegroundColor Yellow
    Write-Host "   - Installing Node.js dependencies" -ForegroundColor Gray
    Write-Host "   - Building React application" -ForegroundColor Gray
    Write-Host "   - Setting up Nginx server" -ForegroundColor Gray
    Write-Host ""
    
    docker build -f Dockerfile.production -t $frontendImage -t $frontendLatest --build-arg VITE_API_URL=http://************ .
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Frontend image built successfully" -ForegroundColor Green
    } else {
        throw "Docker build failed"
    }
} catch {
    Write-Host "Frontend build failed" -ForegroundColor Red
    Write-Host "Common issues:" -ForegroundColor Yellow
    Write-Host "  - Network connectivity (for downloading packages)" -ForegroundColor Gray
    Write-Host "  - Docker Desktop memory/disk space" -ForegroundColor Gray
    Write-Host "  - Node.js build errors" -ForegroundColor Gray
    Set-Location ..
    Read-Host "Press Enter to exit"
    exit 1
}
Set-Location ..

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Build Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Frontend image: $frontendImage" -ForegroundColor Green
Write-Host "Latest tag: $frontendLatest" -ForegroundColor Green
Write-Host ""

# Ask if user wants to push to Docker Hub
$pushChoice = Read-Host "Push images to Docker Hub? (y/N)"
if ($pushChoice -eq "y" -or $pushChoice -eq "Y") {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Pushing to Docker Hub" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    Write-Host "Pushing $frontendImage..." -ForegroundColor Yellow
    docker push $frontendImage
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Versioned frontend image pushed successfully" -ForegroundColor Green
    } else {
        Write-Host "Failed to push versioned frontend image" -ForegroundColor Red
    }
    
    Write-Host "Pushing $frontendLatest..." -ForegroundColor Yellow
    docker push $frontendLatest
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Latest frontend image pushed successfully" -ForegroundColor Green
    } else {
        Write-Host "Failed to push latest frontend image" -ForegroundColor Red
    }
} else {
    Write-Host "Skipping Docker Hub push" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Deployment Commands" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Copy these commands to deploy on your production server:" -ForegroundColor Yellow
Write-Host ""
Write-Host "# Pull new frontend image:" -ForegroundColor White
Write-Host "docker pull $frontendImage" -ForegroundColor Gray
Write-Host ""
Write-Host "# Update docker-compose and restart frontend only:" -ForegroundColor White
Write-Host "FRONTEND_VERSION=$timestamp docker-compose -f docker-compose.production.yml up -d frontend" -ForegroundColor Gray
Write-Host ""
Write-Host "# Or update your docker-compose.production.yml file with:" -ForegroundColor White
Write-Host "#   image: $frontendImage" -ForegroundColor Gray
Write-Host "# Then run: docker-compose -f docker-compose.production.yml up -d frontend" -ForegroundColor Gray
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Frontend Fix Completed!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "What was fixed:" -ForegroundColor Yellow
Write-Host "   - Changed API URL from process-photo to process_photo" -ForegroundColor Gray
Write-Host "   - Frontend now matches backend endpoint exactly" -ForegroundColor Gray
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "   1. Deploy this frontend image to production" -ForegroundColor Gray
Write-Host "   2. Test photo processing functionality" -ForegroundColor Gray
Write-Host ""
Read-Host "Press Enter to exit"
