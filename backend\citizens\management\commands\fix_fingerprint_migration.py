"""
Management command to fix fingerprint migration issues
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command


class Command(BaseCommand):
    help = 'Fix fingerprint migration issues by checking database state'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS('🔧 Checking fingerprint migration state...')
        )
        
        with connection.cursor() as cursor:
            # Check if fingerprint columns exist
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name='citizens_citizen' 
                AND column_name IN ('fingerprint_option', 'no_fingerprint_reason')
                ORDER BY column_name
            """)
            existing_columns = cursor.fetchall()
            
            if existing_columns:
                self.stdout.write("📋 Found existing fingerprint columns:")
                for col_name, data_type, nullable, default in existing_columns:
                    self.stdout.write(f"  - {col_name}: {data_type} (nullable: {nullable}, default: {default})")
            else:
                self.stdout.write("❌ No fingerprint columns found in database")
                
            # Check migration state
            cursor.execute("""
                SELECT name FROM django_migrations 
                WHERE app='citizens' AND name LIKE '%fingerprint%'
                ORDER BY name
            """)
            fingerprint_migrations = cursor.fetchall()
            
            if fingerprint_migrations:
                self.stdout.write("📋 Found fingerprint-related migrations:")
                for (migration_name,) in fingerprint_migrations:
                    self.stdout.write(f"  - {migration_name}")
            else:
                self.stdout.write("❌ No fingerprint migrations found in django_migrations table")
                
            # Check for problematic migration
            cursor.execute("""
                SELECT name FROM django_migrations 
                WHERE app='citizens' AND name='0006_citizen_fingerprint_option_and_more'
            """)
            problematic_migration = cursor.fetchone()
            
            if problematic_migration:
                self.stdout.write(
                    self.style.WARNING("⚠️ Found problematic migration: 0006_citizen_fingerprint_option_and_more")
                )
                
                if not dry_run:
                    # Remove the problematic migration record
                    cursor.execute("""
                        DELETE FROM django_migrations 
                        WHERE app='citizens' AND name='0006_citizen_fingerprint_option_and_more'
                    """)
                    self.stdout.write(
                        self.style.SUCCESS("✅ Removed problematic migration record")
                    )
                else:
                    self.stdout.write("🔍 Would remove problematic migration record (dry-run mode)")
                    
        # Check if our safe migration exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT name FROM django_migrations 
                WHERE app='citizens' AND name='0006_add_fingerprint_option_safe'
            """)
            safe_migration = cursor.fetchone()
            
            if not safe_migration:
                self.stdout.write("📝 Safe migration not applied yet")
                if not dry_run:
                    try:
                        call_command('migrate', 'citizens', '0006_add_fingerprint_option_safe', verbosity=2)
                        self.stdout.write(
                            self.style.SUCCESS("✅ Applied safe fingerprint migration")
                        )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f"❌ Failed to apply safe migration: {e}")
                        )
                else:
                    self.stdout.write("🔍 Would apply safe migration (dry-run mode)")
            else:
                self.stdout.write("✅ Safe migration already applied")
                
        # Final status check
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='citizens_citizen' 
                AND column_name IN ('fingerprint_option', 'no_fingerprint_reason')
            """)
            final_columns = [row[0] for row in cursor.fetchall()]
            
            if len(final_columns) == 2:
                self.stdout.write(
                    self.style.SUCCESS("🎉 Fingerprint columns are properly configured!")
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f"❌ Missing columns. Found: {final_columns}")
                )
                
        if dry_run:
            self.stdout.write(
                self.style.WARNING("🔍 This was a dry run. No changes were made.")
            )
            self.stdout.write("Run without --dry-run to apply fixes.")
        else:
            self.stdout.write(
                self.style.SUCCESS("✅ Migration fix completed!")
            )
