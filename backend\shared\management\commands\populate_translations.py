"""
Django management command to populate translation fields for shared data models.
This command adds translations in Amharic, Oromo, Tigrinya, Somali, and Afar languages.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from shared.models import (
    Country, Region, Religion, CitizenStatus, MaritalStatus, 
    DocumentType, EmploymentType, Relationship, CurrentStatus, 
    BiometricType, Ketena
)


class Command(BaseCommand):
    help = 'Populate translation fields for shared data models'

    def add_arguments(self, parser):
        parser.add_argument(
            '--model',
            type=str,
            help='Specific model to populate (e.g., Country, Religion)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        """Main command handler"""
        self.dry_run = options.get('dry_run', False)
        model_name = options.get('model')
        
        if self.dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        # Define translation mappings
        self.translation_mappings = self.get_translation_mappings()
        
        # Models to process
        models_to_process = {
            'Country': Country,
            'Region': Region,
            'Religion': Religion,
            'CitizenStatus': CitizenStatus,
            'MaritalStatus': MaritalStatus,
            'DocumentType': DocumentType,
            'EmploymentType': EmploymentType,
            'Relationship': Relationship,
            'CurrentStatus': CurrentStatus,
            'BiometricType': BiometricType,
            'Ketena': Ketena,
        }
        
        if model_name:
            if model_name not in models_to_process:
                raise CommandError(f'Model "{model_name}" not found. Available models: {", ".join(models_to_process.keys())}')
            models_to_process = {model_name: models_to_process[model_name]}
        
        # Process each model
        for model_name, model_class in models_to_process.items():
            self.stdout.write(f'\nProcessing {model_name}...')
            self.populate_model_translations(model_class, model_name)
        
        if not self.dry_run:
            self.stdout.write(
                self.style.SUCCESS('\nTranslation population completed successfully!')
            )
        else:
            self.stdout.write(
                self.style.WARNING('\nDry run completed. Use --dry-run=False to apply changes.')
            )

    def get_translation_mappings(self):
        """Define translation mappings for common terms"""
        return {
            # Countries
            'Ethiopia': {
                'am': 'ኢትዮጵያ',
                'om': 'Itoophiyaa',
                'ti': 'ኢትዮጵያ',
                'so': 'Itoobiya',
                'aa': 'Itiyoophiya'
            },
            'United States': {
                'am': 'አሜሪካ',
                'om': 'Ameerikaa',
                'ti': 'አሜሪካ',
                'so': 'Maraykanka',
                'aa': 'Ameerika'
            },
            
            # Religions
            'Orthodox': {
                'am': 'ኦርቶዶክስ',
                'om': 'Ortodoksii',
                'ti': 'ኦርቶዶክስ',
                'so': 'Ortodoks',
                'aa': 'Ortodoks'
            },
            'Muslim': {
                'am': 'ሙስሊም',
                'om': 'Musiliima',
                'ti': 'ሙስሊም',
                'so': 'Muslim',
                'aa': 'Muslim'
            },
            'Protestant': {
                'am': 'ፕሮቴስታንት',
                'om': 'Pirotestaantii',
                'ti': 'ፕሮቴስታንት',
                'so': 'Protestant',
                'aa': 'Protestant'
            },
            'Catholic': {
                'am': 'ካቶሊክ',
                'om': 'Kaatolikii',
                'ti': 'ካቶሊክ',
                'so': 'Katolik',
                'aa': 'Katolik'
            },
            
            # Marital Status
            'Single': {
                'am': 'ያላገባ',
                'om': 'Kan hin fuune',
                'ti': 'ዘይተመርዐ',
                'so': 'Aan guursan',
                'aa': 'Maqaale'
            },
            'Married': {
                'am': 'ያገባ',
                'om': 'Kan fuudhe',
                'ti': 'ዝተመርዐ',
                'so': 'Guursaday',
                'aa': 'Qaalee'
            },
            'Divorced': {
                'am': 'የተፋታ',
                'om': 'Kan hiike',
                'ti': 'ዝተፋተሐ',
                'so': 'Furay',
                'aa': 'Faqate'
            },
            'Widowed': {
                'am': 'የሞተበት/ባት',
                'om': 'Kan dhirsi/niitiin du\'e',
                'ti': 'ዝሞተ ሰብኣይ/ሰበይቲ',
                'so': 'Carmalnimo',
                'aa': 'Maqaale'
            },
            
            # Employment Types
            'Government': {
                'am': 'መንግስት',
                'om': 'Mootummaa',
                'ti': 'መንግስቲ',
                'so': 'Dawlad',
                'aa': 'Dawla'
            },
            'Private': {
                'am': 'ግል',
                'om': 'Dhuunfaa',
                'ti': 'ውልቃዊ',
                'so': 'Gaar',
                'aa': 'Binafsi'
            },
            'Self Employed': {
                'am': 'ራስን ቀጣሪ',
                'om': 'Ofii hojjetaa',
                'ti': 'ናይ ርእሲ ስራሕ',
                'so': 'Shaqaale madax-bannaan',
                'aa': 'Kee hojjetu'
            },
            'Student': {
                'am': 'ተማሪ',
                'om': 'Barataa',
                'ti': 'ተማሃራይ',
                'so': 'Arday',
                'aa': 'Barayttu'
            },
            'Unemployed': {
                'am': 'ስራ አጥ',
                'om': 'Kan hojii hin qabne',
                'ti': 'ዘይሰርሕ',
                'so': 'Aan shaqayn',
                'aa': 'Hojji yaabu'
            },
            
            # Relationships
            'Father': {
                'am': 'አባት',
                'om': 'Abbaa',
                'ti': 'ኣቦ',
                'so': 'Aabe',
                'aa': 'Abba'
            },
            'Mother': {
                'am': 'እናት',
                'om': 'Haadha',
                'ti': 'ኣደ',
                'so': 'Hooyo',
                'aa': 'Ayyo'
            },
            'Spouse': {
                'am': 'ባለቤት',
                'om': 'Hiriyaa',
                'ti': 'መርዓዊ',
                'so': 'Xaas',
                'aa': 'Qaalee'
            },
            'Child': {
                'am': 'ልጅ',
                'om': 'Ijoollee',
                'ti': 'ውላድ',
                'so': 'Ilmo',
                'aa': 'Lak'
            },
            'Sibling': {
                'am': 'ወንድም/እህት',
                'om': 'Obboleessa/obboleettii',
                'ti': 'ሓው/ሓብቲ',
                'so': 'Walaal',
                'aa': 'Giddo'
            },
            
            # Document Types
            'Birth Certificate': {
                'am': 'የልደት ሰርተፊኬት',
                'om': 'Ragaa dhaloota',
                'ti': 'ምስክር ወሊድ',
                'so': 'Shahaadada dhalashada',
                'aa': 'Shahaada dhalashada'
            },
            'Passport': {
                'am': 'ፓስፖርት',
                'om': 'Paaspoortii',
                'ti': 'ፓስፖርት',
                'so': 'Baasaboor',
                'aa': 'Baasaboor'
            },
            'Driver License': {
                'am': 'የመንጃ ፈቃድ',
                'om': 'Waraqaa konkolaataa',
                'ti': 'ፍቓድ ምዝዋር',
                'so': 'Ruqsadda wadista',
                'aa': 'Ruqsad wadista'
            }
        }

    def populate_model_translations(self, model_class, model_name):
        """Populate translations for a specific model"""
        objects = model_class.objects.all()
        updated_count = 0
        
        for obj in objects:
            original_name = obj.name
            translations = self.translation_mappings.get(original_name, {})
            
            if translations:
                updated = False
                
                # Update translation fields
                for lang_code, translation in translations.items():
                    field_name = f'name_{lang_code}'
                    if hasattr(obj, field_name):
                        current_value = getattr(obj, field_name)
                        if not current_value:  # Only update if field is empty
                            if not self.dry_run:
                                setattr(obj, field_name, translation)
                            updated = True
                            self.stdout.write(
                                f'  {original_name} -> {lang_code}: {translation}'
                            )
                
                if updated and not self.dry_run:
                    obj.save()
                    updated_count += 1
            else:
                # For items without predefined translations, we could add placeholder logic here
                self.stdout.write(
                    self.style.WARNING(f'  No translations found for: {original_name}')
                )
        
        if not self.dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'Updated {updated_count} {model_name} objects')
            )
        else:
            self.stdout.write(f'Would update {updated_count} {model_name} objects')
