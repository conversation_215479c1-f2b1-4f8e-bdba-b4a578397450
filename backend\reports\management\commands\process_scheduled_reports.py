from django.core.management.base import BaseCommand
from reports.tasks import ReportSchedulerService, ReportCleanupService


class Command(BaseCommand):
    help = 'Process scheduled reports and cleanup expired ones'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup-only',
            action='store_true',
            help='Only run cleanup, skip scheduled report processing',
        )
        parser.add_argument(
            '--schedule-only',
            action='store_true',
            help='Only process scheduled reports, skip cleanup',
        )

    def handle(self, *args, **options):
        cleanup_only = options['cleanup_only']
        schedule_only = options['schedule_only']

        if not cleanup_only:
            self.stdout.write('Processing scheduled reports...')
            scheduler = ReportSchedulerService()
            results = scheduler.process_scheduled_reports()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Processed {results["processed"]} schedules: '
                    f'{results["successful"]} successful, {results["failed"]} failed'
                )
            )
            
            if results['errors']:
                for error in results['errors']:
                    self.stdout.write(
                        self.style.ERROR(f'Error in {error["schedule"]}: {error["error"]}')
                    )

        if not schedule_only:
            self.stdout.write('Cleaning up expired reports...')
            cleanup_service = ReportCleanupService()
            cleanup_results = cleanup_service.cleanup_expired_reports()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Cleaned up {cleanup_results["deleted"]} expired reports '
                    f'out of {cleanup_results["processed"]} processed'
                )
            )
            
            if cleanup_results['errors']:
                for error in cleanup_results['errors']:
                    self.stdout.write(
                        self.style.ERROR(f'Cleanup error for {error["report"]}: {error["error"]}')
                    )

        self.stdout.write(self.style.SUCCESS('Report processing completed!'))
