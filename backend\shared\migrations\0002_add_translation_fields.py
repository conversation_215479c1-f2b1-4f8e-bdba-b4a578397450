# Generated manually for translation fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shared', '0001_initial'),
    ]

    operations = [
        # Add translation fields to Country
        migrations.AddField(
            model_name='country',
            name='name_am',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='country',
            name='name_om',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='country',
            name='name_ti',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='country',
            name='name_so',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='country',
            name='name_aa',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True, verbose_name='Name (Afar)'),
        ),
        
        # Add translation fields to Region
        migrations.AddField(
            model_name='region',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='region',
            name='name_om',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='region',
            name='name_ti',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='region',
            name='name_so',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='region',
            name='name_aa',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Afar)'),
        ),
        
        # Add translation fields to Religion
        migrations.AddField(
            model_name='religion',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='religion',
            name='name_om',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='religion',
            name='name_ti',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='religion',
            name='name_so',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='religion',
            name='name_aa',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Afar)'),
        ),
        
        # Add translation fields to CitizenStatus
        migrations.AddField(
            model_name='citizenstatus',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='citizenstatus',
            name='name_om',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='citizenstatus',
            name='name_ti',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='citizenstatus',
            name='name_so',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='citizenstatus',
            name='name_aa',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Afar)'),
        ),
        
        # Add translation fields to MaritalStatus
        migrations.AddField(
            model_name='maritalstatus',
            name='name_am',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='maritalstatus',
            name='name_om',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='maritalstatus',
            name='name_ti',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='maritalstatus',
            name='name_so',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='maritalstatus',
            name='name_aa',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Name (Afar)'),
        ),

        # Add translation fields to DocumentType
        migrations.AddField(
            model_name='documenttype',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='documenttype',
            name='name_om',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='documenttype',
            name='name_ti',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='documenttype',
            name='name_so',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='documenttype',
            name='name_aa',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Afar)'),
        ),

        # Add translation fields to EmploymentType
        migrations.AddField(
            model_name='employmenttype',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='employmenttype',
            name='name_om',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='employmenttype',
            name='name_ti',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='employmenttype',
            name='name_so',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='employmenttype',
            name='name_aa',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Afar)'),
        ),

        # Add translation fields to Relationship
        migrations.AddField(
            model_name='relationship',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='relationship',
            name='name_om',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='relationship',
            name='name_ti',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='relationship',
            name='name_so',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='relationship',
            name='name_aa',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Afar)'),
        ),

        # Add translation fields to CurrentStatus
        migrations.AddField(
            model_name='currentstatus',
            name='name_am',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='currentstatus',
            name='name_om',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='currentstatus',
            name='name_ti',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='currentstatus',
            name='name_so',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='currentstatus',
            name='name_aa',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Name (Afar)'),
        ),

        # Add translation fields to BiometricType
        migrations.AddField(
            model_name='biometrictype',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='biometrictype',
            name='name_om',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='biometrictype',
            name='name_ti',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='biometrictype',
            name='name_so',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='biometrictype',
            name='name_aa',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Afar)'),
        ),

        # Add translation fields to Ketena
        migrations.AddField(
            model_name='ketena',
            name='name_am',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Amharic)'),
        ),
        migrations.AddField(
            model_name='ketena',
            name='name_om',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Oromo)'),
        ),
        migrations.AddField(
            model_name='ketena',
            name='name_ti',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Tigrinya)'),
        ),
        migrations.AddField(
            model_name='ketena',
            name='name_so',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Somali)'),
        ),
        migrations.AddField(
            model_name='ketena',
            name='name_aa',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name (Afar)'),
        ),
    ]
