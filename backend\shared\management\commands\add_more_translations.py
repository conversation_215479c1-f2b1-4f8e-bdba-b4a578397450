from django.core.management.base import BaseCommand
from shared.models import Religion, Relationship, Country


class Command(BaseCommand):
    help = 'Add Amharic translations for Religion, Relationship, and Country models'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Adding translations for Religion, Relationship, and Country models...'))
        
        # Religion translations
        religion_translations = {
            'Christianity': 'ክርስትና',
            'Islam': 'እስልምና', 
            'Orthodox': 'ኦርቶዶክስ',
            'Protestant': 'ፕሮቴስታንት',
            'Catholic': 'ካቶሊክ',
            'Catholicism': 'ካቶሊክ',
            'Judaism': 'አይሁድና',
            'Buddhism': 'ቡድሂዝም',
            'Hinduism': 'ሂንዱይዝም',
            'Traditional': 'ባህላዊ',
            'Other': 'ሌላ'
        }

        self.stdout.write('Updating Religion translations...')
        for english_name, amharic_name in religion_translations.items():
            try:
                religion = Religion.objects.get(name=english_name)
                religion.name_am = amharic_name
                religion.save()
                self.stdout.write(f"  ✓ {english_name} -> {amharic_name}")
            except Religion.DoesNotExist:
                self.stdout.write(f"  ✗ Religion '{english_name}' not found")

        # Relationship translations
        relationship_translations = {
            'Father': 'አባት',
            'Mother': 'እናት',
            'Son': 'ወንድ ልጅ',
            'Daughter': 'ሴት ልጅ',
            'Brother': 'ወንድም',
            'Sister': 'እህት',
            'Husband': 'ባል',
            'Wife': 'ሚስት',
            'Spouse': 'ባለቤት',
            'Child': 'ልጅ',
            'Parent': 'ወላጅ',
            'Sibling': 'ወንድም/እህት',
            'Grandfather': 'አያት',
            'Grandmother': 'አያት',
            'Uncle': 'አጎት',
            'Aunt': 'አክስት',
            'Cousin': 'የአጎት/አክስት ልጅ',
            'Friend': 'ጓደኛ',
            'Neighbor': 'ጎረቤት',
            'Guardian': 'አሳዳጊ',
            'Other': 'ሌላ'
        }

        self.stdout.write('\nUpdating Relationship translations...')
        for english_name, amharic_name in relationship_translations.items():
            try:
                relationship = Relationship.objects.get(name=english_name)
                relationship.name_am = amharic_name
                relationship.save()
                self.stdout.write(f"  ✓ {english_name} -> {amharic_name}")
            except Relationship.DoesNotExist:
                self.stdout.write(f"  ✗ Relationship '{english_name}' not found")

        # Country translations (focusing on major countries)
        country_translations = {
            'Ethiopia': 'ኢትዮጵያ',
            'United States': 'አሜሪካ',
            'United Kingdom': 'እንግሊዝ',
            'Germany': 'ጀርመን',
            'France': 'ፈረንሳይ',
            'Italy': 'ጣሊያን',
            'Canada': 'ካናዳ',
            'Australia': 'አውስትራሊያ',
            'Kenya': 'ኬንያ',
            'Sudan': 'ሱዳን',
            'Egypt': 'ግብጽ',
            'Somalia': 'ሶማሊያ',
            'Eritrea': 'ኤርትራ',
            'Djibouti': 'ጅቡቲ',
            'South Sudan': 'ደቡብ ሱዳን',
            'Uganda': 'ዩጋንዳ',
            'Tanzania': 'ታንዛኒያ',
            'Saudi Arabia': 'ሳውዲ አረቢያ',
            'India': 'ህንድ',
            'China': 'ቻይና',
            'Japan': 'ጃፓን',
            'South Africa': 'ደቡብ አፍሪካ',
            'Nigeria': 'ናይጄሪያ',
            'Brazil': 'ብራዚል',
            'Russia': 'ሩሲያ'
        }

        self.stdout.write('\nUpdating Country translations...')
        for english_name, amharic_name in country_translations.items():
            try:
                country = Country.objects.get(name=english_name)
                country.name_am = amharic_name
                country.save()
                self.stdout.write(f"  ✓ {english_name} -> {amharic_name}")
            except Country.DoesNotExist:
                self.stdout.write(f"  ✗ Country '{english_name}' not found")

        self.stdout.write(self.style.SUCCESS('\n✅ Translation update completed!'))
        
        # Show summary
        self.stdout.write('\n📊 Summary:')
        self.stdout.write(f"Religions with translations: {Religion.objects.exclude(name_am__isnull=True).exclude(name_am='').count()}")
        self.stdout.write(f"Relationships with translations: {Relationship.objects.exclude(name_am__isnull=True).exclude(name_am='').count()}")
        self.stdout.write(f"Countries with translations: {Country.objects.exclude(name_am__isnull=True).exclude(name_am='').count()}")
