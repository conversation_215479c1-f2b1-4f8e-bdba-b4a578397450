/**
 * Photo Processing Service
 * Handles photo processing including background removal and enhancement
 */

import axios from '../utils/axios';

class PhotoProcessingService {
  /**
   * Process a photo for ID card use
   * @param {File|string} imageData - File object or base64 string
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} Processed photo data
   */
  async processPhoto(imageData, options = {}) {
    try {
      const {
        removeBackground = true,
        enhance = true,
        style = 'professional'
      } = options;

      const formData = new FormData();
      
      if (imageData instanceof File) {
        formData.append('image', imageData);
      } else if (typeof imageData === 'string') {
        formData.append('image_data', imageData);
      } else {
        throw new Error('Invalid image data format');
      }

      formData.append('remove_background', removeBackground);
      formData.append('enhance', enhance);
      formData.append('style', style);

      const response = await axios.post('/api/idcards/process-photo/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('Photo processing failed:', error);
      throw new Error(
        error.response?.data?.error || 
        'Failed to process photo. Please try again.'
      );
    }
  }

  /**
   * Remove background from a photo
   * @param {File|string} imageData - File object or base64 string
   * @returns {Promise<Object>} Photo with background removed
   */
  async removeBackground(imageData) {
    try {
      const formData = new FormData();
      
      if (imageData instanceof File) {
        formData.append('image', imageData);
      } else if (typeof imageData === 'string') {
        formData.append('image_data', imageData);
      } else {
        throw new Error('Invalid image data format');
      }

      const response = await axios.post('/api/idcards/remove_background/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('Background removal failed:', error);
      throw new Error(
        error.response?.data?.error || 
        'Failed to remove background. Please try again.'
      );
    }
  }

  /**
   * Create ID card optimized photo with specific styling
   * @param {File|string} imageData - File object or base64 string
   * @param {string} style - Photo style ('professional', 'passport', 'license')
   * @returns {Promise<Object>} Styled photo data
   */
  async createIdCardPhoto(imageData, style = 'professional') {
    return this.processPhoto(imageData, {
      removeBackground: true,
      enhance: true,
      style
    });
  }

  /**
   * Convert file to base64
   * @param {File} file - File object
   * @returns {Promise<string>} Base64 string
   */
  fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  }

  /**
   * Convert base64 to blob
   * @param {string} base64 - Base64 string
   * @param {string} mimeType - MIME type
   * @returns {Blob} Blob object
   */
  base64ToBlob(base64, mimeType = 'image/png') {
    const byteCharacters = atob(base64.split(',')[1]);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  /**
   * Validate image file
   * @param {File} file - File object
   * @returns {Object} Validation result
   */
  validateImageFile(file) {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    if (!file) {
      return { valid: false, error: 'No file provided' };
    }

    if (!allowedTypes.includes(file.type)) {
      return { 
        valid: false, 
        error: 'Invalid file type. Please use JPEG, PNG, or WebP images.' 
      };
    }

    if (file.size > maxSize) {
      return { 
        valid: false, 
        error: 'File size too large. Please use images smaller than 10MB.' 
      };
    }

    return { valid: true };
  }

  /**
   * Resize image to specific dimensions
   * @param {string} base64 - Base64 image string
   * @param {number} maxWidth - Maximum width
   * @param {number} maxHeight - Maximum height
   * @returns {Promise<string>} Resized base64 image
   */
  resizeImage(base64, maxWidth = 800, maxHeight = 1000) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw resized image
        ctx.drawImage(img, 0, 0, width, height);

        // Convert to base64
        resolve(canvas.toDataURL('image/png', 0.9));
      };

      img.src = base64;
    });
  }

  /**
   * Compress image quality
   * @param {string} base64 - Base64 image string
   * @param {number} quality - Quality (0-1)
   * @returns {Promise<string>} Compressed base64 image
   */
  compressImage(base64, quality = 0.8) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        resolve(canvas.toDataURL('image/jpeg', quality));
      };

      img.src = base64;
    });
  }

  /**
   * Get image dimensions
   * @param {string} base64 - Base64 image string
   * @returns {Promise<Object>} Image dimensions
   */
  getImageDimensions(base64) {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
          aspectRatio: img.width / img.height
        });
      };
      img.src = base64;
    });
  }

  /**
   * Create image preview
   * @param {File} file - File object
   * @returns {Promise<string>} Preview base64 string
   */
  async createPreview(file) {
    try {
      const validation = this.validateImageFile(file);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      let base64 = await this.fileToBase64(file);
      
      // Resize if too large
      const dimensions = await this.getImageDimensions(base64);
      if (dimensions.width > 800 || dimensions.height > 1000) {
        base64 = await this.resizeImage(base64, 800, 1000);
      }

      return base64;
    } catch (error) {
      console.error('Preview creation failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
export default new PhotoProcessingService();
