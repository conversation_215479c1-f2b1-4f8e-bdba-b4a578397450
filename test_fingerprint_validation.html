<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fingerprint Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-case.pass {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .test-case.fail {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-input {
            margin: 10px 0;
            padding: 10px;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .result {
            font-weight: bold;
            margin-top: 10px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>Fingerprint Registration Validation Test</h1>
    <p>This test simulates the validation logic for the fingerprint registration step.</p>

    <div id="test-container">
        <h2>Interactive Test</h2>
        <div class="test-case">
            <h3>Test the Validation Logic</h3>
            
            <label>
                <input type="radio" name="fingerprint_option" value="with_fingerprint" onchange="updateValidation()"> 
                With Fingerprint
            </label><br>
            
            <label>
                <input type="radio" name="fingerprint_option" value="without_fingerprint" onchange="updateValidation()"> 
                Without Fingerprint
            </label><br><br>

            <div id="fingerprint-fields" style="display: none;">
                <label>Left Thumb Fingerprint:</label>
                <input type="text" id="left_thumb" placeholder="Fingerprint data..." onchange="updateValidation()">
                <br><br>
                <label>Right Thumb Fingerprint:</label>
                <input type="text" id="right_thumb" placeholder="Fingerprint data..." onchange="updateValidation()">
            </div>

            <div id="reason-field" style="display: none;">
                <label>Reason for No Fingerprint:</label>
                <textarea id="no_fingerprint_reason" placeholder="Enter reason..." onchange="updateValidation()" oninput="updateValidation()"></textarea>
            </div>

            <div class="result">
                <p>Validation Result: <span id="validation-result">Not tested</span></p>
                <button id="next-button" disabled>Next</button>
            </div>
        </div>
    </div>

    <div id="automated-tests">
        <h2>Automated Test Cases</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <div id="test-results"></div>
    </div>

    <script>
        // Simulate the validation logic from the React component
        function validateFingerprintStep(values) {
            // First check if fingerprint_option is selected
            if (!values.fingerprint_option) {
                return { valid: false, error: 'Please select fingerprint option' };
            }

            if (values.fingerprint_option === 'with_fingerprint') {
                // Both thumbs required when choosing fingerprint option
                if (!values.left_thumb_fingerprint) {
                    return { valid: false, error: 'Left thumb fingerprint is required' };
                }
                if (!values.right_thumb_fingerprint) {
                    return { valid: false, error: 'Right thumb fingerprint is required' };
                }
                return { valid: true, error: null };
            } else if (values.fingerprint_option === 'without_fingerprint') {
                // Reason required when choosing no fingerprint option - check for non-empty string
                if (!values.no_fingerprint_reason || values.no_fingerprint_reason.trim().length === 0) {
                    return { valid: false, error: 'Please specify reason for no fingerprint' };
                }
                return { valid: true, error: null };
            }

            return { valid: false, error: 'Invalid fingerprint option' };
        }

        function updateValidation() {
            const fingerprintOption = document.querySelector('input[name="fingerprint_option"]:checked')?.value;
            const leftThumb = document.getElementById('left_thumb').value;
            const rightThumb = document.getElementById('right_thumb').value;
            const reason = document.getElementById('no_fingerprint_reason').value;

            // Show/hide relevant fields
            const fingerprintFields = document.getElementById('fingerprint-fields');
            const reasonField = document.getElementById('reason-field');
            
            if (fingerprintOption === 'with_fingerprint') {
                fingerprintFields.style.display = 'block';
                reasonField.style.display = 'none';
            } else if (fingerprintOption === 'without_fingerprint') {
                fingerprintFields.style.display = 'none';
                reasonField.style.display = 'block';
            } else {
                fingerprintFields.style.display = 'none';
                reasonField.style.display = 'none';
            }

            // Validate
            const values = {
                fingerprint_option: fingerprintOption,
                left_thumb_fingerprint: leftThumb,
                right_thumb_fingerprint: rightThumb,
                no_fingerprint_reason: reason
            };

            const result = validateFingerprintStep(values);
            const resultElement = document.getElementById('validation-result');
            const nextButton = document.getElementById('next-button');

            if (result.valid) {
                resultElement.textContent = 'Valid ✅';
                resultElement.style.color = 'green';
                nextButton.disabled = false;
            } else {
                resultElement.textContent = `Invalid ❌ - ${result.error}`;
                resultElement.style.color = 'red';
                nextButton.disabled = true;
            }
        }

        function runTestCase(description, values, expectedValid) {
            const result = validateFingerprintStep(values);
            const passed = result.valid === expectedValid;
            
            return {
                description,
                values,
                expectedValid,
                actualValid: result.valid,
                error: result.error,
                passed
            };
        }

        function runAllTests() {
            const testCases = [
                {
                    description: "No option selected",
                    values: {},
                    expectedValid: false
                },
                {
                    description: "With fingerprint - both thumbs provided",
                    values: {
                        fingerprint_option: 'with_fingerprint',
                        left_thumb_fingerprint: 'fingerprint_data_left',
                        right_thumb_fingerprint: 'fingerprint_data_right'
                    },
                    expectedValid: true
                },
                {
                    description: "With fingerprint - missing left thumb",
                    values: {
                        fingerprint_option: 'with_fingerprint',
                        left_thumb_fingerprint: '',
                        right_thumb_fingerprint: 'fingerprint_data_right'
                    },
                    expectedValid: false
                },
                {
                    description: "With fingerprint - missing right thumb",
                    values: {
                        fingerprint_option: 'with_fingerprint',
                        left_thumb_fingerprint: 'fingerprint_data_left',
                        right_thumb_fingerprint: ''
                    },
                    expectedValid: false
                },
                {
                    description: "Without fingerprint - valid reason provided",
                    values: {
                        fingerprint_option: 'without_fingerprint',
                        no_fingerprint_reason: 'Medical condition - missing fingers'
                    },
                    expectedValid: true
                },
                {
                    description: "Without fingerprint - empty reason",
                    values: {
                        fingerprint_option: 'without_fingerprint',
                        no_fingerprint_reason: ''
                    },
                    expectedValid: false
                },
                {
                    description: "Without fingerprint - whitespace only reason",
                    values: {
                        fingerprint_option: 'without_fingerprint',
                        no_fingerprint_reason: '   \n\t   '
                    },
                    expectedValid: false
                },
                {
                    description: "Without fingerprint - valid reason with whitespace",
                    values: {
                        fingerprint_option: 'without_fingerprint',
                        no_fingerprint_reason: '  Severe scarring from accident  '
                    },
                    expectedValid: true
                }
            ];

            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = '';

            let passedCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = runTestCase(testCase.description, testCase.values, testCase.expectedValid);
                
                const testDiv = document.createElement('div');
                testDiv.className = `test-case ${result.passed ? 'pass' : 'fail'}`;
                testDiv.innerHTML = `
                    <h4>Test ${index + 1}: ${result.description}</h4>
                    <p><strong>Expected:</strong> ${result.expectedValid ? 'Valid' : 'Invalid'}</p>
                    <p><strong>Actual:</strong> ${result.actualValid ? 'Valid' : 'Invalid'}</p>
                    ${result.error ? `<p><strong>Error:</strong> ${result.error}</p>` : ''}
                    <p><strong>Result:</strong> ${result.passed ? '✅ PASS' : '❌ FAIL'}</p>
                `;
                
                resultsContainer.appendChild(testDiv);
                
                if (result.passed) passedCount++;
            });

            // Add summary
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-case ${passedCount === totalCount ? 'pass' : 'fail'}`;
            summaryDiv.innerHTML = `
                <h3>Test Summary</h3>
                <p><strong>Passed:</strong> ${passedCount}/${totalCount}</p>
                <p><strong>Success Rate:</strong> ${Math.round((passedCount/totalCount) * 100)}%</p>
            `;
            resultsContainer.insertBefore(summaryDiv, resultsContainer.firstChild);
        }

        // Initialize
        updateValidation();
    </script>
</body>
</html>
