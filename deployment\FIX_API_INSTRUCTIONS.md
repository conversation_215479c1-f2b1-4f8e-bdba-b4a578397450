# 🔧 Fix Frontend API Configuration for Nginx

## Problem
The frontend is still trying to access the API using port 8000 directly:
```
Failed to load resource: :8000/api/tenants/system-settings/ (404 Not Found)
```

This happens because the frontend is configured to use `http://************:8000` but now nginx handles everything on port 80.

## Solution
We need to:
1. ✅ **Update frontend configuration** to use `http://************` (no port)
2. ✅ **Build new frontend image** with nginx-compatible API URLs
3. ✅ **Push to Docker Hub** 
4. ✅ **Update server** to use the new image

## Step 1: Build and Push Fixed Frontend Image

Run this on your development machine:

```bash
# Navigate to your project directory
cd /path/to/your/goid/project

# Run the fix and push script
chmod +x deployment/fix-and-push-frontend.sh
bash deployment/fix-and-push-frontend.sh
```

This will:
- ✅ Update all frontend API configurations
- ✅ Build new Docker image with nginx-compatible URLs
- ✅ Push to `aragawmebratu/goid-production:frontend-nginx-fixed`

## Step 2: Update Server with New Image

Run this on your server (as root in goid-deployment folder):

```bash
# Navigate to deployment folder
cd /home/<USER>/goid-deployment

# Upload and run the update script
chmod +x update-frontend-on-server.sh
bash update-frontend-on-server.sh
```

This will:
- ✅ Update docker-compose.yml to use the new image
- ✅ Pull the new frontend image
- ✅ Restart frontend service
- ✅ Test the API connectivity

## Expected Results

After running both scripts:

### ✅ **Before (Broken):**
```
Frontend tries: http://************:8000/api/tenants/system-settings/
Result: 404 Not Found (bypasses nginx)
```

### ✅ **After (Fixed):**
```
Frontend tries: http://************/api/tenants/system-settings/
Nginx routes to: backend:8000/api/tenants/system-settings/
Result: 200 OK with JSON data
```

## Files Changed

### Frontend Configuration Files:
- `frontend/public/config.js` - Runtime API URL
- `frontend/src/utils/axios.js` - Dynamic API URL detection
- `frontend/.env.production` - Build-time API URL
- `frontend/src/services/sharedDataService.js` - Public API calls
- `frontend/src/components/idcards/IDCardTemplate.jsx` - ID card API calls

### Changes Made:
```diff
- API_URL: 'http://************:8000'
+ API_URL: 'http://************'

- return `http://${currentHost}:8000`;
+ return `http://${currentHost}`;

- VITE_API_URL=http://************:8000
+ VITE_API_URL=http://************
```

## Verification

After deployment, test these URLs:

```bash
# These should all work now:
curl http://************/api/common/language/info/
curl http://************/api/tenants/system-settings/
curl http://************/admin/

# Frontend should load without console errors:
curl http://************/
```

## Rollback if Needed

If something goes wrong:

```bash
# On server, rollback docker-compose.yml:
cp docker-compose.yml.frontend-update.backup.* docker-compose.yml
docker-compose up -d frontend

# Or use the previous image:
# Change image back to: aragawmebratu/goid-production:frontend-latest
```

## Docker Hub Images

After running the scripts, you'll have:
- `aragawmebratu/goid-production:frontend-nginx-fixed` - New nginx-compatible image
- `aragawmebratu/goid-production:frontend-latest` - Updated latest tag

This fix will completely resolve the API access issues you're seeing in the browser console!
