# GoID Docker Build and Test Script (PowerShell)
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  GoID Docker Build and Test Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Docker is available
Write-Host "Checking Docker availability..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker is available: $dockerVersion" -ForegroundColor Green
    } else {
        throw "Docker command failed"
    }
} catch {
    Write-Host "❌ Docker is not available or not running" -ForegroundColor Red
    Write-Host "Please install Docker Desktop and ensure it's running" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Docker daemon is running
Write-Host "Checking Docker daemon..." -ForegroundColor Yellow
try {
    docker info | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker daemon is running" -ForegroundColor Green
    } else {
        throw "Docker daemon not running"
    }
} catch {
    Write-Host "❌ Docker daemon is not running" -ForegroundColor Red
    Write-Host "Please start Docker Desktop" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Building Backend Docker Image" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Generate timestamp for version
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$backendImage = "aragawmebratu/goid-production:backend-$timestamp"
$backendLatest = "aragawmebratu/goid-production:backend-latest"

Write-Host "Building backend image: $backendImage" -ForegroundColor Yellow
Write-Host ""

# Build backend image
Set-Location backend
try {
    Write-Host "🔄 Building Docker image (this may take 5-10 minutes)..." -ForegroundColor Yellow
    Write-Host "   - Installing system dependencies" -ForegroundColor Gray
    Write-Host "   - Installing Python packages" -ForegroundColor Gray
    Write-Host "   - Downloading AI model (~176MB)" -ForegroundColor Gray
    Write-Host ""

    docker build -f Dockerfile.production -t $backendImage -t $backendLatest . --progress=plain
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Backend image built successfully" -ForegroundColor Green
    } else {
        throw "Docker build failed"
    }
} catch {
    Write-Host "❌ Backend build failed" -ForegroundColor Red
    Write-Host "Common issues:" -ForegroundColor Yellow
    Write-Host "  - Network connectivity (for downloading packages/models)" -ForegroundColor Gray
    Write-Host "  - Docker Desktop memory/disk space" -ForegroundColor Gray
    Write-Host "  - Package repository issues" -ForegroundColor Gray
    Set-Location ..
    Read-Host "Press Enter to exit"
    exit 1
}
Set-Location ..

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Testing Photo Processing in Container" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Create a temporary container to test photo processing
Write-Host "Creating test container..." -ForegroundColor Yellow
try {
    docker run --rm -d --name goid-test-backend $backendImage tail -f /dev/null | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to create test container"
    }
} catch {
    Write-Host "❌ Failed to create test container" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Testing rembg availability..." -ForegroundColor Yellow
$rembgTest = @"
try:
    from rembg import remove, new_session
    print('✅ rembg imported successfully')
    session = new_session('u2net')
    print('✅ u2net session created successfully')
    print('✅ Background removal should work')
except ImportError as e:
    print(f'❌ rembg import failed: {e}')
except Exception as e:
    print(f'❌ rembg session creation failed: {e}')
"@

docker exec goid-test-backend python -c $rembgTest

Write-Host "Testing PhotoProcessor class..." -ForegroundColor Yellow
$processorTest = @"
import sys
sys.path.append('/app')
try:
    from idcards.photo_processing import PhotoProcessor, REMBG_AVAILABLE, CV2_AVAILABLE
    print(f'✅ PhotoProcessor imported successfully')
    print(f'   REMBG_AVAILABLE: {REMBG_AVAILABLE}')
    print(f'   CV2_AVAILABLE: {CV2_AVAILABLE}')
    processor = PhotoProcessor()
    print(f'   Session initialized: {processor.session is not None}')
except Exception as e:
    print(f'❌ PhotoProcessor test failed: {e}')
"@

docker exec goid-test-backend python -c $processorTest

Write-Host "Testing with sample image..." -ForegroundColor Yellow
$imageTest = @"
import sys
sys.path.append('/app')
try:
    from PIL import Image
    import io
    from idcards.photo_processing import PhotoProcessor
    
    # Create test image
    test_image = Image.new('RGB', (100, 100), color='red')
    img_buffer = io.BytesIO()
    test_image.save(img_buffer, format='PNG')
    img_bytes = img_buffer.getvalue()
    
    # Test processing
    processor = PhotoProcessor()
    result = processor.process_id_photo(img_bytes, remove_bg=True, enhance=True)
    
    print(f'✅ Photo processing test completed')
    print(f'   Success: {result.get(\"success\", False)}')
    print(f'   Background removed: {result.get(\"background_removed\", False)}')
    print(f'   REMBG available: {result.get(\"rembg_available\", False)}')
    
    if result.get('error'):
        print(f'   Error: {result[\"error\"]}')
        
except Exception as e:
    print(f'❌ Sample image test failed: {e}')
"@

docker exec goid-test-backend python -c $imageTest

# Clean up test container
Write-Host "Cleaning up test container..." -ForegroundColor Yellow
docker stop goid-test-backend | Out-Null

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Build Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "✅ Backend image: $backendImage" -ForegroundColor Green
Write-Host "✅ Latest tag: $backendLatest" -ForegroundColor Green
Write-Host ""

# Ask if user wants to push to Docker Hub
$pushChoice = Read-Host "Push images to Docker Hub? (y/N)"
if ($pushChoice -eq "y" -or $pushChoice -eq "Y") {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Pushing to Docker Hub" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    Write-Host "Pushing $backendImage..." -ForegroundColor Yellow
    docker push $backendImage
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Versioned image pushed successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to push versioned image" -ForegroundColor Red
    }
    
    Write-Host "Pushing $backendLatest..." -ForegroundColor Yellow
    docker push $backendLatest
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Latest image pushed successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to push latest image" -ForegroundColor Red
    }
} else {
    Write-Host "⏭️  Skipping Docker Hub push" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Deployment Commands" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Copy these commands to deploy on your production server:" -ForegroundColor Yellow
Write-Host ""
Write-Host "# Pull new image:" -ForegroundColor White
Write-Host "docker pull $backendImage" -ForegroundColor Gray
Write-Host ""
Write-Host "# Update docker-compose and restart:" -ForegroundColor White
Write-Host "BACKEND_VERSION=$timestamp docker-compose -f docker-compose.production.yml up -d" -ForegroundColor Gray
Write-Host ""
Write-Host "# Or update your docker-compose.production.yml file with:" -ForegroundColor White
Write-Host "#   image: $backendImage" -ForegroundColor Gray
Write-Host "# Then run: docker-compose -f docker-compose.production.yml up -d" -ForegroundColor Gray
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Build Completed Successfully!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
