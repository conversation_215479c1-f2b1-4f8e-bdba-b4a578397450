# GoID Photo Processing Fix and Deployment Workflow

## Problem
The AI-powered background removal feature is not working because the rembg library and its models are not properly configured in the Docker environment.

## Solution Overview
1. **Fix Docker Image**: Update Dockerfile to include image processing dependencies and pre-download rembg models
2. **Build and Test**: Use automated scripts to build and test the Docker image
3. **Deploy**: Push to Docker Hub and update production server

## Step-by-Step Workflow

### Step 1: Build and Test on Windows PC

Since Python is not installed on your Windows PC, use the Docker-based build scripts:

#### Option A: Using PowerShell (Recommended)
```powershell
# Open PowerShell as Administrator
cd C:\Users\<USER>\Desktop\GoID

# Run the build and test script
.\build_and_test_docker.ps1
```

#### Option B: Using Command Prompt
```cmd
# Open Command Prompt as Administrator
cd C:\Users\<USER>\Desktop\GoID

# Run the build and test script
build_and_test_docker.bat
```

### Step 2: What the Script Does

The build script will:
1. ✅ **Check Docker availability** - Ensures Docker Desktop is running
2. ✅ **Build backend image** - Creates new Docker image with photo processing fixes
3. ✅ **Test rembg functionality** - Verifies background removal works in container
4. ✅ **Test PhotoProcessor** - Confirms the photo processing class works
5. ✅ **Test with sample image** - Runs actual background removal test
6. ✅ **Push to Docker Hub** - Uploads image for production deployment (optional)

### Step 3: Expected Test Results

You should see output like:
```
✅ rembg imported successfully
✅ u2net session created successfully
✅ Background removal should work
✅ PhotoProcessor imported successfully
   REMBG_AVAILABLE: True
   CV2_AVAILABLE: True
   Session initialized: True
✅ Photo processing test completed
   Success: True
   Background removed: True
   REMBG available: True
```

### Step 4: Deploy to Production Server

After successful build and test, the script will provide deployment commands:

#### SSH to Production Server
```bash
ssh user@your-production-server
cd /path/to/goid/project
```

#### Pull New Image
```bash
# Use the specific version from build script output
docker pull aragawmebratu/goid-production:backend-YYYYMMDD-HHMMSS
```

#### Update and Restart Services
```bash
# Method 1: Using environment variable
BACKEND_VERSION=YYYYMMDD-HHMMSS docker-compose -f docker-compose.production.yml up -d

# Method 2: Update docker-compose.production.yml file
# Edit the file to use the specific image tag, then:
docker-compose -f docker-compose.production.yml up -d
```

### Step 5: Verify Deployment

#### Check Container Logs
```bash
# Check if rembg loaded successfully
docker-compose -f docker-compose.production.yml logs backend | grep -i rembg

# Check photo processing logs
docker-compose -f docker-compose.production.yml logs backend | grep -i photo
```

#### Test Photo Processing API
```bash
# Test the API endpoint (replace with your server details)
curl -X POST http://your-server:8000/api/idcards/process_photo/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "image_data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
    "remove_background": true,
    "enhance": true
  }'
```

Expected response:
```json
{
  "success": true,
  "background_removed": true,
  "rembg_available": true,
  "processed_image": "data:image/png;base64,..."
}
```

## Troubleshooting

### If Build Fails
1. **Docker not running**: Start Docker Desktop
2. **Permission issues**: Run PowerShell/CMD as Administrator
3. **Network issues**: Check internet connection for downloading dependencies

### If Tests Fail
1. **rembg import failed**: Dependencies missing (should be fixed by updated Dockerfile)
2. **Model download failed**: Network issue during Docker build
3. **Memory issues**: Increase Docker Desktop memory allocation

### If Deployment Fails
1. **Image not found**: Ensure image was pushed to Docker Hub successfully
2. **Container won't start**: Check logs with `docker-compose logs backend`
3. **API not responding**: Verify container is running and ports are accessible

## Files Modified

### Updated Files:
- ✅ `backend/Dockerfile.production` - Added image processing dependencies and rembg model download
- ✅ `docker-compose.production.yml` - Updated to use Docker Hub images instead of local builds
- ✅ `backend/idcards/photo_processing.py` - Improved error handling and logging

### New Files:
- 📄 `build_and_test_docker.ps1` - PowerShell build and test script
- 📄 `build_and_test_docker.bat` - Batch file build and test script
- 📄 `DEPLOYMENT_WORKFLOW.md` - This deployment guide

## Key Improvements

1. **Docker Image**: Now includes all necessary system dependencies for image processing
2. **Model Pre-download**: rembg models are downloaded during Docker build, not at runtime
3. **Better Error Handling**: Improved logging and fallback mechanisms
4. **Automated Testing**: Scripts verify functionality before deployment
5. **Production Ready**: Uses Docker Hub images for consistent deployments

## Next Steps

1. Run the build script on your Windows PC
2. Verify all tests pass
3. Push the image to Docker Hub
4. Deploy to production server using the provided commands
5. Test the photo processing functionality in the live application

The background removal feature should now work correctly in your production environment!
