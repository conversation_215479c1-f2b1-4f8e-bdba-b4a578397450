#!/bin/bash

# <PERSON>ript to pull and deploy updated backend service with middleware fix
echo "🔄 Pulling and Deploying Updated Backend Service"
echo "==============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
DOCKER_HUB_USERNAME="aragawmebratu"
IMAGE_NAME="goid-production"
BACKEND_TAG="backend-middleware-fixed"

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found"
    print_error "Please run this script from the goid-deployment directory"
    exit 1
fi

print_success "Found docker-compose.yml"

# Show current backend service configuration
print_status "Current backend service in docker-compose.yml:"
echo "=============================================="
grep -A 15 -B 2 "backend:" docker-compose.yml
echo "=============================================="
echo ""

# Backup current docker-compose.yml
BACKUP_FILE="docker-compose.yml.backend-update.backup.$(date +%Y%m%d_%H%M%S)"
print_status "Creating backup: $BACKUP_FILE"
cp docker-compose.yml "$BACKUP_FILE"

# Update the backend image in docker-compose.yml
print_status "Updating backend image to use middleware-fixed version..."

# Replace the backend image line
sed -i "s|image: aragawmebratu/goid-production:backend-latest|image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG|g" docker-compose.yml

# Also update any other backend image references
sed -i "s|aragawmebratu/goid-production:backend-.*|$DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG|g" docker-compose.yml

print_success "✅ Updated docker-compose.yml"

# Show updated configuration
print_status "Updated backend service configuration:"
echo "======================================"
grep -A 15 -B 2 "backend:" docker-compose.yml
echo "======================================"
echo ""

# Ask for confirmation
read -p "🚀 Deploy the updated backend service? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Deployment cancelled"
    print_status "💾 Backup saved as: $BACKUP_FILE"
    exit 0
fi

# Pull the new image
print_status "Pulling new backend image from Docker Hub..."
docker pull "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"

if [ $? -eq 0 ]; then
    print_success "✅ Successfully pulled new backend image"
else
    print_error "❌ Failed to pull new backend image"
    print_error "Make sure the image exists on Docker Hub"
    print_error "Run the build script first: bash fix-and-push-backend.sh"
    exit 1
fi

# Stop and restart backend service
print_status "Restarting backend service..."
docker-compose stop backend
docker-compose rm -f backend
docker-compose up -d backend

# Wait for service to start
print_status "Waiting for backend service to start..."
sleep 30

# Check service status
print_status "Checking service status..."
docker-compose ps backend

# Test the backend
print_status "Testing backend service..."
echo "Backend health: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:8000/admin/ 2>/dev/null || echo 'Failed')"

# Test system settings API through nginx
print_status "Testing system settings API through nginx..."
API_RESPONSE=$(curl -s -o /dev/null -w '%{http_code}' http://localhost/api/tenants/system-settings/ 2>/dev/null)
if [ "$API_RESPONSE" = "200" ]; then
    print_success "✅ System settings API working (HTTP $API_RESPONSE)"
    
    # Get the actual response
    print_status "System settings response:"
    curl -s http://localhost/api/tenants/system-settings/ | python3 -m json.tool 2>/dev/null || echo "Response received but not JSON"
else
    print_warning "⚠️  System settings API response: HTTP $API_RESPONSE"
fi

# Test other API endpoints
print_status "Testing other API endpoints..."
echo "Language info: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/api/common/language/info/ 2>/dev/null || echo 'Failed')"
echo "Admin panel: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/admin/ 2>/dev/null || echo 'Failed')"

# Show final status
echo ""
print_success "🎉 Backend service updated successfully!"
echo ""
print_status "📋 What was updated:"
print_status "  ✅ Backend image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
print_status "  ✅ Middleware fix: /api/tenants/system-settings/ added to public_paths"
print_status "  ✅ Service restarted with new configuration"
echo ""
print_status "🧪 Test your application:"
print_status "  🌐 Frontend: http://************"
print_status "  🔧 System Settings: http://************/api/tenants/system-settings/"
print_status "  🔌 API: http://************/api/"
print_status "  ⚙️  Admin: http://************/admin/"
echo ""
print_status "💾 Backup saved as: $BACKUP_FILE"
print_status "🔄 To rollback if needed: cp $BACKUP_FILE docker-compose.yml && docker-compose up -d backend"

echo ""
print_success "The system settings API should now work without 404 errors!"
print_status "Check your browser - the 'Failed to load system settings' error should be resolved."
