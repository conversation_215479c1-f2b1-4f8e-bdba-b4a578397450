# Production Dockerfile for GoID Backend
FROM python:3.10-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=goid.settings

# Set work directory
WORKDIR /app

# Install system dependencies including Java for FMatcher and image processing
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
        libusb-1.0-0-dev \
        pkg-config \
        gettext \
        default-jre-headless \
        # Image processing dependencies for rembg and OpenCV
        libgl1-mesa-glx \
        libglib2.0-0 \
        libsm6 \
        libxext6 \
        libxrender-dev \
        libgomp1 \
        libgthread-2.0-0 \
        libfontconfig1 \
        libgtk-3-0 \
        # Additional dependencies for PIL/Pillow
        libjpeg-dev \
        libpng-dev \
        libtiff-dev \
        libfreetype6-dev \
        liblcms2-dev \
        libwebp-dev \
        libharfbuzz-dev \
        libfribidi-dev \
        libxcb1-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Pre-download rembg models to avoid runtime delays
RUN python -c "\
try:\
    from rembg import new_session;\
    print('Downloading rembg u2net model...');\
    session = new_session('u2net');\
    print('rembg model downloaded successfully')\
except Exception as e:\
    print(f'rembg model download failed: {e}');\
    print('Background removal will use fallback processing')\
"

# Copy project
COPY . /app/

# Make startup script executable
RUN chmod +x /app/startup.sh

# Create directories for static and media files
RUN mkdir -p /app/static /app/media

# Collect static files
RUN python manage.py collectstatic --noinput

# Compile translation files
RUN python manage.py compilemessages

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python manage.py check --database default || exit 1

# Run the application with complete initialization
CMD ["bash", "/app/startup.sh"]
