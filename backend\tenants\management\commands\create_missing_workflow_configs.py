#!/usr/bin/env python3
"""
Management command to create missing workflow configurations for existing kebeles.
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from tenants.models import Tenant, TenantType
from tenants.models_workflow import TenantWorkflowConfig


class Command(BaseCommand):
    help = 'Create missing workflow configurations for existing kebele tenants'

    def add_arguments(self, parser):
        parser.add_argument(
            '--workflow-type',
            type=str,
            default='centralized',
            choices=['centralized', 'autonomous', 'hybrid'],
            help='Default workflow type for kebeles without configuration (default: centralized)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating configurations'
        )

    def handle(self, *args, **options):
        workflow_type = options['workflow_type']
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS('🔍 Checking Workflow Configurations for All Kebeles')
        )
        self.stdout.write('=' * 60)
        
        # Get all kebele tenants
        kebeles = Tenant.objects.filter(type=TenantType.KEBELE)
        self.stdout.write(f'Total Kebeles: {kebeles.count()}')
        self.stdout.write()
        
        missing_configs = []
        existing_configs = []
        
        # Check each kebele for workflow configuration
        for kebele in kebeles:
            try:
                config = kebele.workflow_config
                existing_configs.append({
                    'kebele': kebele,
                    'workflow_type': config.workflow_type
                })
                self.stdout.write(
                    f'✅ {kebele.name} (ID: {kebele.id}) - {config.workflow_type}'
                )
            except TenantWorkflowConfig.DoesNotExist:
                missing_configs.append(kebele)
                self.stdout.write(
                    self.style.WARNING(f'❌ {kebele.name} (ID: {kebele.id}) - NO CONFIG')
                )
        
        self.stdout.write()
        self.stdout.write(f'📊 Summary:')
        self.stdout.write(f'   Kebeles with config: {len(existing_configs)}')
        self.stdout.write(f'   Kebeles missing config: {len(missing_configs)}')
        
        if not missing_configs:
            self.stdout.write(
                self.style.SUCCESS('✅ All kebeles already have workflow configurations!')
            )
            return
        
        self.stdout.write()
        self.stdout.write('🚨 Kebeles missing workflow configuration:')
        for kebele in missing_configs:
            self.stdout.write(f'   - {kebele.name} (ID: {kebele.id})')
        
        if dry_run:
            self.stdout.write()
            self.stdout.write(
                self.style.WARNING('🔍 DRY RUN: Would create configurations but not actually creating them')
            )
            self.stdout.write(f'   Default workflow type: {workflow_type}')
            return
        
        # Create missing configurations
        self.stdout.write()
        self.stdout.write(f'🔧 Creating missing workflow configurations (type: {workflow_type})')
        self.stdout.write('=' * 60)
        
        created_count = 0
        
        with transaction.atomic():
            for kebele in missing_configs:
                try:
                    config = self.create_workflow_config(kebele, workflow_type)
                    if config:
                        created_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(f'✅ Created {workflow_type} config for {kebele.name}')
                        )
                    else:
                        self.stdout.write(
                            self.style.ERROR(f'❌ Failed to create config for {kebele.name}')
                        )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'❌ Error creating config for {kebele.name}: {e}')
                    )
        
        self.stdout.write()
        self.stdout.write(f'📊 Results:')
        self.stdout.write(f'   Configurations created: {created_count}')
        self.stdout.write(f'   Failed: {len(missing_configs) - created_count}')
        
        if created_count > 0:
            self.stdout.write()
            self.stdout.write(
                self.style.SUCCESS('🎉 Workflow configurations created successfully!')
            )
            self.stdout.write()
            self.stdout.write('Next steps:')
            self.stdout.write('1. All kebeles now have proper workflow configurations')
            self.stdout.write('2. ID card approvals will follow the configured workflow')
            self.stdout.write('3. You can switch workflows using the management interface')
        else:
            self.stdout.write()
            self.stdout.write(
                self.style.WARNING('⚠️ No configurations were created')
            )

    def create_workflow_config(self, kebele, workflow_type):
        """Create workflow configuration for a kebele."""
        try:
            if workflow_type == 'centralized':
                config_data = {
                    'workflow_type': 'centralized',
                    'id_card_processing': {
                        'can_print_locally': False,
                        'requires_higher_approval': True,
                        'approval_levels': ['kebele_leader', 'subcity_admin'],
                        'printing_authority': 'subcity',
                        'quality_control': 'centralized'
                    },
                    'citizen_registration': {
                        'full_workflow_local': True,
                        'requires_external_verification': True,
                        'biometric_processing': 'local',
                        'document_verification': 'hierarchical'
                    },
                    'approval_workflow': {
                        'final_approval_level': 'subcity_admin',
                        'escalation_required': True,
                        'auto_approve_threshold': None,
                        'post_approval_printing': False
                    },
                    'document_verification': {
                        'verification_authority': 'subcity',
                        'external_verification_required': True,
                        'verification_levels': ['kebele_leader', 'subcity_admin']
                    }
                }
            elif workflow_type == 'autonomous':
                config_data = {
                    'workflow_type': 'autonomous',
                    'id_card_processing': {
                        'can_print_locally': True,
                        'requires_higher_approval': False,
                        'approval_levels': ['kebele_leader'],
                        'printing_authority': 'kebele',
                        'quality_control': 'local'
                    },
                    'citizen_registration': {
                        'full_workflow_local': True,
                        'requires_external_verification': False,
                        'biometric_processing': 'local',
                        'document_verification': 'local'
                    },
                    'approval_workflow': {
                        'final_approval_level': 'kebele_leader',
                        'escalation_required': False,
                        'auto_approve_threshold': None,
                        'post_approval_printing': True
                    },
                    'document_verification': {
                        'verification_authority': 'kebele',
                        'external_verification_required': False,
                        'verification_levels': ['kebele_leader']
                    }
                }
            else:  # hybrid
                config_data = {
                    'workflow_type': 'hybrid',
                    'id_card_processing': {
                        'can_print_locally': True,
                        'requires_higher_approval': True,
                        'approval_levels': ['kebele_leader', 'subcity_admin'],
                        'printing_authority': 'kebele',
                        'quality_control': 'mixed'
                    },
                    'citizen_registration': {
                        'full_workflow_local': True,
                        'requires_external_verification': False,
                        'biometric_processing': 'local',
                        'document_verification': 'local'
                    },
                    'approval_workflow': {
                        'final_approval_level': 'kebele_leader',
                        'escalation_required': False,
                        'auto_approve_threshold': 95,
                        'post_approval_printing': True
                    },
                    'document_verification': {
                        'verification_authority': 'kebele',
                        'external_verification_required': False,
                        'verification_levels': ['kebele_leader']
                    }
                }
            
            config, created = TenantWorkflowConfig.objects.get_or_create(
                tenant=kebele,
                defaults=config_data
            )
            
            return config if created else None
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating config for {kebele.name}: {e}')
            )
            return None
