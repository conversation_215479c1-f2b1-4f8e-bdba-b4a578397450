#!/bin/bash

# Quick deployment script for GoID on UoG server
echo "🚀 GoID Quick Deployment Script"
echo "==============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root or with sudo
if [ "$EUID" -eq 0 ]; then
    print_warning "Running as root. This is not recommended for production."
fi

# Stop any existing containers
print_status "Stopping existing containers..."
docker-compose down 2>/dev/null || true

# Pull latest images
print_status "Pulling latest Docker images..."
docker pull aragawmebratu/goid-production:backend-latest
docker pull aragawmebratu/goid-production:frontend-latest
docker pull postgres:15
docker pull redis:7-alpine
docker pull dpage/pgadmin4:latest

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p backups
mkdir -p logs

# Copy the fixed docker-compose file
print_status "Using fixed Docker Compose configuration..."
cp docker-compose.production-fixed.yml docker-compose.yml

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    print_status "Creating environment file..."
    cp .env.production .env
    print_warning "Please edit .env file to set your production passwords and secrets!"
fi

# Start services
print_status "Starting services..."
docker-compose up -d

# Wait for services to start
print_status "Waiting for services to start..."
sleep 30

# Check service status
print_status "Checking service status..."
docker-compose ps

# Test backend connectivity
print_status "Testing backend connectivity..."
for i in {1..10}; do
    if curl -f http://localhost:8000/api/health/ 2>/dev/null; then
        print_success "Backend is responding!"
        break
    else
        print_status "Attempt $i/10: Backend not ready yet, waiting..."
        sleep 10
    fi
done

# Test with server IP
SERVER_IP=$(hostname -I | awk '{print $1}')
print_status "Testing with server IP: $SERVER_IP"
curl -f http://$SERVER_IP:8000/api/health/ 2>/dev/null && print_success "Backend accessible via server IP" || print_warning "Backend not accessible via server IP"

# Show final status
print_status "Deployment complete!"
print_status "Services should be available at:"
print_status "  Frontend: http://$SERVER_IP:3000"
print_status "  Backend API: http://$SERVER_IP:8000"
print_status "  PgAdmin: http://$SERVER_IP:5050"

print_status "To troubleshoot issues, run: bash troubleshoot-backend.sh"
print_status "To view logs, run: docker-compose logs -f [service_name]"
