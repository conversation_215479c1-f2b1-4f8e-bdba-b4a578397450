#!/usr/bin/env python3
"""
<PERSON>ript to fix rembg model download issues
This script will download and verify the u2net model for background removal
"""

import os
import sys
import urllib.request
import hashlib
from pathlib import Path

def download_with_progress(url, filename):
    """Download file with progress indicator"""
    def progress_hook(block_num, block_size, total_size):
        downloaded = block_num * block_size
        if total_size > 0:
            percent = min(100, (downloaded * 100) // total_size)
            print(f"\r📥 Downloading {filename}: {percent}% ({downloaded}/{total_size} bytes)", end='')
        else:
            print(f"\r📥 Downloading {filename}: {downloaded} bytes", end='')
    
    try:
        urllib.request.urlretrieve(url, filename, progress_hook)
        print()  # New line after progress
        return True
    except Exception as e:
        print(f"\n❌ Download failed: {e}")
        return False

def verify_file_hash(filepath, expected_hash):
    """Verify file integrity using SHA256 hash"""
    try:
        with open(filepath, 'rb') as f:
            file_hash = hashlib.sha256(f.read()).hexdigest()
        
        if file_hash.lower() == expected_hash.lower():
            print(f"✅ File integrity verified: {filepath.name}")
            return True
        else:
            print(f"❌ File integrity check failed: {filepath.name}")
            print(f"   Expected: {expected_hash}")
            print(f"   Got:      {file_hash}")
            return False
    except Exception as e:
        print(f"❌ Hash verification failed: {e}")
        return False

def setup_rembg_models():
    """Download and setup rembg models"""
    print("=" * 60)
    print("SETTING UP REMBG MODELS")
    print("=" * 60)
    
    # Model information
    models = {
        'u2net': {
            'url': 'https://github.com/danielgatis/rembg/releases/download/v0.0.0/u2net.onnx',
            'filename': 'u2net.onnx',
            'hash': '347c3d51b7b4b9a8e5e5b7b5b5b5b5b5b5b5b5b5b5b5b5b5b5b5b5b5b5b5b5b5'  # Placeholder
        }
    }
    
    # Determine model directory
    model_dirs = [
        Path.home() / '.u2net',
        Path.home() / '.cache' / 'rembg',
        Path(__file__).parent / 'models'
    ]
    
    # Try to find existing model directory or create one
    model_dir = None
    for dir_path in model_dirs:
        if dir_path.exists():
            model_dir = dir_path
            print(f"📁 Found existing model directory: {model_dir}")
            break
    
    if not model_dir:
        # Create default model directory
        model_dir = Path.home() / '.u2net'
        model_dir.mkdir(parents=True, exist_ok=True)
        print(f"📁 Created model directory: {model_dir}")
    
    # Check and download models
    for model_name, model_info in models.items():
        model_path = model_dir / model_info['filename']
        
        if model_path.exists():
            print(f"✅ Model already exists: {model_path}")
            size_mb = model_path.stat().st_size / (1024 * 1024)
            print(f"   Size: {size_mb:.1f} MB")
            
            # Verify integrity if hash is available
            if model_info['hash'] and model_info['hash'] != 'placeholder':
                verify_file_hash(model_path, model_info['hash'])
        else:
            print(f"📥 Downloading {model_name} model...")
            print(f"   URL: {model_info['url']}")
            print(f"   Destination: {model_path}")
            
            if download_with_progress(model_info['url'], model_path):
                print(f"✅ Downloaded: {model_path}")
                size_mb = model_path.stat().st_size / (1024 * 1024)
                print(f"   Size: {size_mb:.1f} MB")
            else:
                print(f"❌ Failed to download: {model_name}")
                return False
    
    return True

def test_rembg_functionality():
    """Test rembg functionality after model setup"""
    print("\n" + "=" * 60)
    print("TESTING REMBG FUNCTIONALITY")
    print("=" * 60)
    
    try:
        from rembg import remove, new_session
        from PIL import Image
        import io
        
        print("✅ rembg imported successfully")
        
        # Create session
        session = new_session('u2net')
        print("✅ u2net session created")
        
        # Create test image
        test_image = Image.new('RGB', (100, 100), color='red')
        
        # Convert to bytes
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='PNG')
        img_bytes = img_buffer.getvalue()
        
        print("🔄 Testing background removal...")
        
        # Remove background
        output = remove(img_bytes, session=session)
        
        if output:
            result_image = Image.open(io.BytesIO(output))
            print("✅ Background removal test successful")
            print(f"   Input size: {test_image.size}")
            print(f"   Output size: {result_image.size}")
            print(f"   Output mode: {result_image.mode}")
            return True
        else:
            print("❌ Background removal returned empty result")
            return False
            
    except ImportError as e:
        print(f"❌ rembg import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ rembg test failed: {e}")
        return False

def install_rembg():
    """Install or upgrade rembg"""
    print("\n" + "=" * 60)
    print("INSTALLING/UPGRADING REMBG")
    print("=" * 60)
    
    try:
        import subprocess
        
        # Upgrade pip first
        print("🔄 Upgrading pip...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
        
        # Install rembg with specific version
        print("🔄 Installing rembg...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'rembg==2.0.41'])
        
        print("✅ rembg installation completed")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        return False

def main():
    """Main function"""
    print("GoID rembg Model Setup and Troubleshooting Tool")
    print("=" * 60)
    
    # Check if rembg is installed
    try:
        import rembg
        print(f"✅ rembg is installed (version: {rembg.__version__})")
    except ImportError:
        print("❌ rembg is not installed")
        
        install_choice = input("🤔 Install rembg now? (y/N): ").lower().strip()
        if install_choice in ['y', 'yes']:
            if not install_rembg():
                print("❌ Installation failed. Please install manually:")
                print("   pip install rembg==2.0.41")
                sys.exit(1)
        else:
            print("❌ rembg is required for background removal")
            sys.exit(1)
    
    # Setup models
    if setup_rembg_models():
        print("\n✅ Model setup completed")
    else:
        print("\n❌ Model setup failed")
        sys.exit(1)
    
    # Test functionality
    if test_rembg_functionality():
        print("\n✅ rembg is working correctly!")
        print("   Background removal should now work in the application")
    else:
        print("\n❌ rembg test failed")
        print("   Background removal may not work properly")
    
    print("\n" + "=" * 60)
    print("SETUP COMPLETED")
    print("=" * 60)
    print("Next steps:")
    print("1. Restart your Django development server")
    print("2. Test photo processing in the application")
    print("3. If issues persist, check the Django logs")

if __name__ == '__main__':
    main()
