# GoID Photo Processing Troubleshooting Guide

## Overview
This guide helps diagnose and fix issues with the AI-powered background removal functionality in the GoID system.

## Quick Diagnosis

### Step 1: Run Diagnostic Script (Windows PC)
```bash
cd backend
python check_photo_processing.py
```

This will check:
- ✅ Required dependencies (PIL, rembg, opencv, numpy)
- ✅ rembg model availability
- ✅ PhotoProcessor class functionality
- ✅ Background removal with test image

### Step 2: Test API Endpoints (Windows PC)
```bash
# Start Django development server first
python manage.py runserver

# In another terminal, run API test
python test_photo_api.py
```

This will test:
- ✅ Photo processing API endpoint
- ✅ Background removal API endpoint
- ✅ Response format and success status

## Common Issues and Solutions

### Issue 1: rembg Not Available
**Symptoms:**
- Console shows: "rembg not available" or "Background removal will be disabled"
- Photos are enhanced but background is not removed

**Solution:**
```bash
# Install rembg
pip install rembg==2.0.41

# Or run the fix script
python fix_rembg_models.py
```

### Issue 2: Model Download Failed
**Symptoms:**
- "Failed to initialize rembg session" error
- rembg imports but session creation fails

**Solution:**
```bash
# Run model setup script
python fix_rembg_models.py

# Or manually download models
python -c "from rembg import new_session; new_session('u2net')"
```

### Issue 3: Memory Issues
**Symptoms:**
- Background removal works but is very slow
- Out of memory errors during processing

**Solution:**
1. Reduce image size before processing
2. Use u2net_lite model instead:
```python
# In photo_processing.py, change line 49:
self.session = new_session('u2net_lite')  # Instead of 'u2net'
```

### Issue 4: OpenCV Not Available
**Symptoms:**
- "opencv-python not available" warning
- Advanced image processing disabled

**Solution:**
```bash
pip install opencv-python-headless==********
```

## Development Workflow

### 1. Check and Fix Issues (Windows PC)
```bash
# Navigate to project directory
cd C:\path\to\GoID

# Check photo processing
cd backend
python check_photo_processing.py

# Fix any issues found
python fix_rembg_models.py

# Test API endpoints
python manage.py runserver  # In one terminal
python test_photo_api.py    # In another terminal
```

### 2. Build Docker Images (Windows PC)
```bash
# Return to project root
cd ..

# Run build script
python build_and_deploy.py
```

This will:
- ✅ Check Docker availability
- ✅ Verify photo processing functionality
- ✅ Build backend and frontend Docker images
- ✅ Push to Docker Hub (optional)
- ✅ Generate deployment commands

### 3. Deploy to Production Server (SSH)
```bash
# SSH to production server
ssh user@your-production-server

# Pull new images (use version from build script output)
docker pull aragawmebratu/goid-production:backend-YYYYMMDD-HHMMSS
docker pull aragawmebratu/goid-frontend:frontend-YYYYMMDD-HHMMSS

# Update docker-compose and restart
BACKEND_VERSION=YYYYMMDD-HHMMSS FRONTEND_VERSION=YYYYMMDD-HHMMSS docker-compose up -d

# Or update docker-compose.yml with new version tags and run:
docker-compose up -d
```

## Verification Steps

### After Deployment
1. **Check container logs:**
```bash
docker-compose logs backend | grep -i rembg
docker-compose logs backend | grep -i photo
```

2. **Test photo processing via API:**
```bash
# Create test request
curl -X POST http://your-server:8000/api/idcards/process_photo/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"image_data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==", "remove_background": true}'
```

3. **Check response:**
```json
{
  "success": true,
  "background_removed": true,
  "rembg_available": true,
  "processed_image": "data:image/png;base64,..."
}
```

## Docker Configuration

### Dockerfile Requirements
Ensure your backend Dockerfile includes:
```dockerfile
# Install system dependencies for image processing
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Pre-download rembg models (optional, for faster startup)
RUN python -c "from rembg import new_session; new_session('u2net')"
```

## Monitoring and Logs

### Key Log Messages to Watch For:
- ✅ `"rembg imported successfully. Background removal is available."`
- ✅ `"rembg session initialized successfully"`
- ✅ `"Background removed successfully"`
- ❌ `"rembg not available"`
- ❌ `"Failed to initialize rembg session"`
- ❌ `"Background removal failed"`

### Django Settings for Logging:
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'photo_processing.log',
        },
    },
    'loggers': {
        'idcards.photo_processing': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## Performance Optimization

### For Production:
1. **Use u2net_lite model** for faster processing
2. **Implement image size limits** (max 2MB, 1920x1080)
3. **Add caching** for processed images
4. **Use async processing** for large batches

### Memory Management:
```python
# In photo_processing.py, add memory cleanup
import gc

def remove_background(self, image_data):
    try:
        # ... existing code ...
        result = Image.open(io.BytesIO(output))
        
        # Clean up memory
        del output
        gc.collect()
        
        return result
    except Exception as e:
        # ... error handling ...
```

## Support and Troubleshooting

If issues persist:
1. Check system requirements (RAM > 4GB recommended)
2. Verify internet connection for model downloads
3. Check disk space for model files (~100MB)
4. Review Docker container resource limits
5. Monitor system memory usage during processing

For additional help, check the application logs and container resource usage.
