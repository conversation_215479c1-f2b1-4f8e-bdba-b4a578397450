#!/bin/bash

# GoID Nginx Check and Deploy Script
echo "🔍 GoID Current Setup Check and Nginx Deployment"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check current directory
print_status "Current directory: $(pwd)"
print_status "Files in current directory:"
ls -la

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found in current directory"
    exit 1
fi

print_success "Found docker-compose.yml"

# Show current docker-compose.yml content (first 50 lines)
print_status "Current docker-compose.yml content (first 50 lines):"
echo "=================================================="
head -50 docker-compose.yml
echo "=================================================="

# Check current running services
print_status "Current running services:"
docker-compose ps

# Check which ports are currently exposed
print_status "Current port bindings:"
docker-compose ps | grep -E ":[0-9]+->"

# Check if nginx service exists in current docker-compose.yml
if grep -q "nginx:" docker-compose.yml; then
    print_success "Nginx service already exists in docker-compose.yml"
    NGINX_EXISTS=true
else
    print_warning "Nginx service not found in docker-compose.yml"
    NGINX_EXISTS=false
fi

# Check if port 80 is being used
if grep -q "80:80" docker-compose.yml; then
    print_success "Port 80 is configured in docker-compose.yml"
    PORT_80_CONFIGURED=true
else
    print_warning "Port 80 is not configured in docker-compose.yml"
    PORT_80_CONFIGURED=false
fi

# Test current connectivity
print_status "Testing current connectivity..."
echo "Testing port 80:"
curl -I http://localhost:80 2>/dev/null && print_success "Port 80 accessible" || print_warning "Port 80 not accessible"

echo "Testing port 3000:"
curl -I http://localhost:3000 2>/dev/null && print_success "Port 3000 accessible" || print_warning "Port 3000 not accessible"

echo "Testing port 8000:"
curl -I http://localhost:8000 2>/dev/null && print_success "Port 8000 accessible" || print_warning "Port 8000 not accessible"

# Provide recommendations
print_status "Analysis and Recommendations:"
echo "============================================"

if [ "$NGINX_EXISTS" = false ] && [ "$PORT_80_CONFIGURED" = false ]; then
    print_warning "Current setup does not include nginx reverse proxy"
    print_status "Recommendation: Deploy nginx configuration to fix API access issues"
    
    echo ""
    print_status "Would you like to:"
    print_status "1. Backup current docker-compose.yml"
    print_status "2. Deploy nginx reverse proxy configuration"
    print_status "3. Test the new setup"
    echo ""
    
    read -p "Proceed with nginx deployment? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Backup current configuration
        BACKUP_FILE="docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)"
        print_status "Backing up current docker-compose.yml to $BACKUP_FILE"
        cp docker-compose.yml "$BACKUP_FILE"
        
        # Check if nginx configuration files exist
        if [ -f "nginx.conf" ] && [ -f "docker-compose.nginx.yml" ]; then
            print_status "Deploying nginx configuration..."
            
            # Stop current services
            print_status "Stopping current services..."
            docker-compose down
            
            # Replace docker-compose.yml with nginx version
            cp docker-compose.nginx.yml docker-compose.yml
            
            # Start services with nginx
            print_status "Starting services with nginx..."
            docker-compose up -d
            
            # Wait for services to start
            print_status "Waiting for services to start..."
            sleep 30
            
            # Test new setup
            print_status "Testing new nginx setup..."
            echo "Testing port 80:"
            curl -I http://localhost:80 2>/dev/null && print_success "Port 80 now accessible!" || print_error "Port 80 still not accessible"
            
            echo "Testing API through nginx:"
            curl -f http://localhost/api/common/language/info/ 2>/dev/null && print_success "API accessible through nginx!" || print_error "API not accessible through nginx"
            
            echo "Testing frontend through nginx:"
            curl -I http://localhost/ 2>/dev/null && print_success "Frontend accessible through nginx!" || print_error "Frontend not accessible through nginx"
            
            # Show final status
            print_status "Final service status:"
            docker-compose ps
            
            print_success "Nginx deployment completed!"
            print_status "Your application is now available at:"
            print_status "  Main App: http://************"
            print_status "  API: http://************/api/"
            print_status "  Admin: http://************/admin/"
            
        else
            print_error "nginx.conf or docker-compose.nginx.yml not found"
            print_error "Please ensure all nginx configuration files are present"
        fi
    else
        print_status "Nginx deployment cancelled"
    fi
    
elif [ "$NGINX_EXISTS" = true ]; then
    print_success "Nginx is already configured"
    print_status "Checking if nginx service is running..."
    
    if docker-compose ps nginx | grep -q "Up"; then
        print_success "Nginx service is running"
        print_status "Checking nginx configuration..."
        docker-compose exec nginx nginx -t && print_success "Nginx configuration is valid" || print_error "Nginx configuration has errors"
    else
        print_warning "Nginx service is not running"
        print_status "Attempting to start nginx..."
        docker-compose up -d nginx
    fi
    
else
    print_status "Current configuration analysis complete"
fi

print_status "Script completed!"
