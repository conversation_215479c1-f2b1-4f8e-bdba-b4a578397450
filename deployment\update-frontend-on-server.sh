#!/bin/bash

# Script to update frontend service on server with nginx-compatible image
echo "🔄 Updating Frontend Service with Nginx-Compatible Image"
echo "========================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
DOCKER_HUB_USERNAME="aragawmebratu"
IMAGE_NAME="goid-production"
FRONTEND_TAG="frontend-nginx-fixed"

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found"
    print_error "Please run this script from the goid-deployment directory"
    exit 1
fi

print_success "Found docker-compose.yml"

# Show current frontend service configuration
print_status "Current frontend service in docker-compose.yml:"
echo "=============================================="
grep -A 10 -B 2 "frontend:" docker-compose.yml
echo "=============================================="
echo ""

# Backup current docker-compose.yml
BACKUP_FILE="docker-compose.yml.frontend-update.backup.$(date +%Y%m%d_%H%M%S)"
print_status "Creating backup: $BACKUP_FILE"
cp docker-compose.yml "$BACKUP_FILE"

# Update the frontend image in docker-compose.yml
print_status "Updating frontend image to use nginx-compatible version..."

# Replace the frontend image line
sed -i "s|image: aragawmebratu/goid-production:frontend-latest|image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG|g" docker-compose.yml

# Also update any other frontend image references
sed -i "s|aragawmebratu/goid-production:frontend-.*|$DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG|g" docker-compose.yml

# Update environment variables to remove port 8000
sed -i 's|REACT_APP_API_URL=http://************:8000|REACT_APP_API_URL=http://************|g' docker-compose.yml
sed -i 's|VITE_API_URL=http://************:8000|VITE_API_URL=http://************|g' docker-compose.yml

print_success "✅ Updated docker-compose.yml"

# Show updated configuration
print_status "Updated frontend service configuration:"
echo "======================================"
grep -A 10 -B 2 "frontend:" docker-compose.yml
echo "======================================"
echo ""

# Ask for confirmation
read -p "🚀 Deploy the updated frontend service? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Deployment cancelled"
    print_status "💾 Backup saved as: $BACKUP_FILE"
    exit 0
fi

# Pull the new image
print_status "Pulling new frontend image..."
docker pull "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"

if [ $? -eq 0 ]; then
    print_success "✅ Successfully pulled new frontend image"
else
    print_error "❌ Failed to pull new frontend image"
    print_error "Make sure the image exists on Docker Hub"
    exit 1
fi

# Stop and restart frontend service
print_status "Restarting frontend service..."
docker-compose stop frontend
docker-compose rm -f frontend
docker-compose up -d frontend

# Wait for service to start
print_status "Waiting for frontend service to start..."
sleep 15

# Check service status
print_status "Checking service status..."
docker-compose ps frontend

# Test the frontend
print_status "Testing frontend service..."
echo "Frontend health: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/ 2>/dev/null || echo 'Failed')"
echo "Frontend via nginx: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:80/ 2>/dev/null || echo 'Failed')"

# Test API access through nginx
print_status "Testing API access through nginx..."
API_RESPONSE=$(curl -s -o /dev/null -w '%{http_code}' http://localhost/api/common/language/info/ 2>/dev/null)
if [ "$API_RESPONSE" = "200" ]; then
    print_success "✅ API accessible through nginx (HTTP $API_RESPONSE)"
else
    print_warning "⚠️  API response: HTTP $API_RESPONSE"
fi

# Show final status
echo ""
print_success "🎉 Frontend service updated successfully!"
echo ""
print_status "📋 What was updated:"
print_status "  ✅ Frontend image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"
print_status "  ✅ API URL: http://************ (no port 8000)"
print_status "  ✅ Service restarted with new configuration"
echo ""
print_status "🧪 Test your application:"
print_status "  🌐 Frontend: http://************"
print_status "  🔌 API: http://************/api/"
print_status "  ⚙️  Admin: http://************/admin/"
echo ""
print_status "💾 Backup saved as: $BACKUP_FILE"
print_status "🔄 To rollback if needed: cp $BACKUP_FILE docker-compose.yml && docker-compose up -d frontend"

echo ""
print_success "The frontend should now make API calls without the :8000 port!"
print_status "Check your browser console - the 404 errors should be resolved."
