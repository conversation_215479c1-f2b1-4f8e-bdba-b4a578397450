#!/bin/bash

# Deploy Fingerprint Fix to GoID Server
# This script updates the docker-compose.yml and deploys the fixed images

set -e  # Exit on any error

echo "🚀 Deploying GoID Fingerprint Fix to Server..."

# Configuration
DOCKER_USERNAME="aragawmebratu"
FRONTEND_IMAGE="$DOCKER_USERNAME/goid-production:frontend-fingerprint-fix"
BACKEND_IMAGE="$DOCKER_USERNAME/goid-production:backend-fingerprint-fix"
COMPOSE_FILE="docker-compose.yml"
BACKUP_FILE="docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if docker-compose.yml exists
if [ ! -f "$COMPOSE_FILE" ]; then
    print_error "docker-compose.yml not found in current directory"
    print_error "Please run this script from the deployment directory"
    exit 1
fi

print_status "Found docker-compose.yml ✅"

# Create backup
print_status "Creating backup of docker-compose.yml..."
cp "$COMPOSE_FILE" "$BACKUP_FILE"
print_success "Backup created: $BACKUP_FILE"

# Update docker-compose.yml with new images
print_status "Updating docker-compose.yml with new images..."

# Update frontend image
sed -i.tmp "s|image: aragawmebratu/goid-production:frontend-fingerprint-option|image: $FRONTEND_IMAGE|g" "$COMPOSE_FILE"

# Update backend image  
sed -i.tmp "s|image: aragawmebratu/goid-production:backend-fingerprint-option|image: $BACKEND_IMAGE|g" "$COMPOSE_FILE"

# Remove temporary file
rm -f "$COMPOSE_FILE.tmp"

print_success "docker-compose.yml updated with new images"

# Show the changes
print_status "Updated images:"
echo "  Frontend: $FRONTEND_IMAGE"
echo "  Backend:  $BACKEND_IMAGE"

# Pull new images
print_status "Pulling new Docker images..."
if docker-compose pull; then
    print_success "Images pulled successfully"
else
    print_error "Failed to pull images"
    print_warning "Restoring backup..."
    cp "$BACKUP_FILE" "$COMPOSE_FILE"
    exit 1
fi

# Stop current containers
print_status "Stopping current containers..."
docker-compose down

# Start with new images
print_status "Starting containers with new images..."
if docker-compose up -d; then
    print_success "Containers started successfully"
else
    print_error "Failed to start containers"
    print_warning "Restoring backup and restarting..."
    cp "$BACKUP_FILE" "$COMPOSE_FILE"
    docker-compose up -d
    exit 1
fi

# Fix migration issues if they exist
print_status "Checking and fixing migration issues..."
if docker-compose exec backend python manage.py fix_fingerprint_migration --dry-run; then
    print_status "Running migration fix..."
    docker-compose exec backend python manage.py fix_fingerprint_migration
    print_success "Migration issues resolved"
else
    print_warning "Migration fix command not available or failed"
fi

# Wait a moment for containers to start
sleep 10

# Check container status
print_status "Checking container status..."
docker-compose ps

# Test if the application is responding
print_status "Testing application health..."
if curl -f http://localhost/api/health/ > /dev/null 2>&1; then
    print_success "Application is responding ✅"
else
    print_warning "Application health check failed - this might be normal during startup"
fi

# Summary
echo ""
echo "🎉 Deployment Complete!"
echo "========================"
echo "✅ Backup created: $BACKUP_FILE"
echo "✅ Images updated and pulled"
echo "✅ Containers restarted"
echo ""
echo "🔧 Changes deployed:"
echo "- Fixed fingerprint registration 'Next' button issue"
echo "- Improved validation logic for 'without fingerprint' option"
echo "- Enhanced real-time validation feedback"
echo "- Better error messages for validation failures"
echo ""
echo "📋 Next steps:"
echo "1. Test the fingerprint registration flow"
echo "2. Verify 'Next' button works when selecting 'without fingerprint'"
echo "3. If issues occur, restore backup with:"
echo "   cp $BACKUP_FILE $COMPOSE_FILE && docker-compose up -d"
echo ""
echo "🌐 Access your application at:"
echo "   Frontend: http://$(hostname -I | awk '{print $1}')/"
echo "   Backend:  http://$(hostname -I | awk '{print $1}'):8000/"
