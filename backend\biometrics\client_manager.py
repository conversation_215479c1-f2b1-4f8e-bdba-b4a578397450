"""
GoID Biometric Client Manager
Manages connections to distributed biometric capture clients
"""

import logging
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)

class BiometricClientManager:
    """
    Manages connections to biometric capture clients running on kebele machines
    """
    
    def __init__(self):
        self.timeout = getattr(settings, 'BIOMETRIC_CLIENT_TIMEOUT', 30)
        self.default_port = 8002
        self.cache_timeout = 300  # 5 minutes
        
    def get_client_url_from_request(self, request) -> Optional[str]:
        """
        Determine client URL from request context
        Priority: 1. Header, 2. Session, 3. Auto-discovery
        """
        # Check for explicit client URL in headers
        client_url = request.META.get('HTTP_X_BIOMETRIC_CLIENT_URL')
        if client_url:
            logger.info(f"Using biometric client from header: {client_url}")
            return client_url
        
        # Check session for stored client URL
        client_url = request.session.get('biometric_client_url')
        if client_url:
            logger.info(f"Using biometric client from session: {client_url}")
            return client_url
        
        # Try to auto-discover from client IP
        client_ip = self.get_client_ip(request)
        if client_ip:
            discovered_url = self.discover_client_service(client_ip)
            if discovered_url:
                # Store in session for future requests
                request.session['biometric_client_url'] = discovered_url
                logger.info(f"Auto-discovered biometric client: {discovered_url}")
                return discovered_url
        
        # Fallback to localhost (for development/testing)
        fallback_url = f"http://localhost:{self.default_port}"
        logger.warning(f"No biometric client found, using fallback: {fallback_url}")
        return fallback_url
    
    def get_client_ip(self, request) -> Optional[str]:
        """Extract client IP address from request"""
        # Check for forwarded headers first
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(',')[0].strip()
        
        # Check for real IP header
        real_ip = request.META.get('HTTP_X_REAL_IP')
        if real_ip:
            return real_ip
        
        # Fall back to remote address
        return request.META.get('REMOTE_ADDR')
    
    def discover_client_service(self, client_ip: str) -> Optional[str]:
        """
        Try to discover biometric service on client machine
        """
        cache_key = f"biometric_client_{client_ip}"
        cached_url = cache.get(cache_key)
        if cached_url:
            return cached_url
        
        # Try common ports and protocols
        candidates = [
            f"http://{client_ip}:{self.default_port}",
            f"https://{client_ip}:{self.default_port}",
            f"http://{client_ip}:8001",  # Alternative port
            f"http://{client_ip}:8003",  # Alternative port
        ]
        
        for url in candidates:
            if self.test_client_connection(url):
                # Cache successful discovery
                cache.set(cache_key, url, self.cache_timeout)
                return url
        
        return None
    
    def test_client_connection(self, client_url: str) -> bool:
        """Test if biometric client is available at given URL"""
        try:
            health_url = f"{client_url}/api/health"
            response = requests.get(health_url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                # Verify it's actually our biometric service
                if data.get('status') == 'healthy' and 'device_connected' in data:
                    logger.info(f"✅ Biometric client verified at {client_url}")
                    return True
            
        except Exception as e:
            logger.debug(f"Client test failed for {client_url}: {e}")
        
        return False
    
    def capture_fingerprint(self, request, thumb_type: str) -> Dict:
        """
        Capture fingerprint from appropriate client
        """
        client_url = self.get_client_url_from_request(request)
        if not client_url:
            return {
                'success': False,
                'error': 'No biometric client available',
                'error_code': 'NO_CLIENT'
            }
        
        try:
            capture_url = f"{client_url}/api/capture/fingerprint"
            
            logger.info(f"🔍 Capturing {thumb_type} thumb from {client_url}")
            
            response = requests.post(
                capture_url,
                json={'thumb_type': thumb_type},
                timeout=self.timeout,
                headers={'User-Agent': 'GoID-Server/1.0'}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    logger.info(f"✅ Successfully captured fingerprint from {client_url}")
                    return data
                else:
                    logger.error(f"❌ Capture failed: {data.get('error')}")
                    return data
            else:
                logger.error(f"❌ HTTP {response.status_code}: {response.text}")
                return {
                    'success': False,
                    'error': f'Client returned HTTP {response.status_code}',
                    'error_code': 'HTTP_ERROR'
                }
                
        except requests.exceptions.Timeout:
            logger.error(f"❌ Timeout connecting to {client_url}")
            return {
                'success': False,
                'error': f'Timeout connecting to biometric client at {client_url}',
                'error_code': 'TIMEOUT'
            }
        except requests.exceptions.ConnectionError:
            logger.error(f"❌ Connection failed to {client_url}")
            return {
                'success': False,
                'error': f'Cannot connect to biometric client at {client_url}. Please ensure the biometric service is running on the client machine.',
                'error_code': 'CONNECTION_ERROR'
            }
        except Exception as e:
            logger.error(f"❌ Unexpected error: {e}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'error_code': 'UNKNOWN_ERROR'
            }
    
    def get_client_status(self, request) -> Dict:
        """Get status of biometric client"""
        client_url = self.get_client_url_from_request(request)
        if not client_url:
            return {
                'connected': False,
                'error': 'No biometric client available'
            }
        
        try:
            status_url = f"{client_url}/api/device/status"
            response = requests.get(status_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                data['client_url'] = client_url
                return data
            else:
                return {
                    'connected': False,
                    'error': f'HTTP {response.status_code}',
                    'client_url': client_url
                }
                
        except Exception as e:
            return {
                'connected': False,
                'error': str(e),
                'client_url': client_url
            }
    
    def register_client(self, request, client_info: Dict) -> Dict:
        """
        Register a biometric client (called by client on startup)
        """
        client_ip = self.get_client_ip(request)
        client_url = client_info.get('service_url')
        
        if not client_url:
            client_url = f"http://{client_ip}:{self.default_port}"
        
        # Test the connection
        if self.test_client_connection(client_url):
            # Cache the client
            cache_key = f"biometric_client_{client_ip}"
            cache.set(cache_key, client_url, self.cache_timeout * 4)  # Longer cache for registered clients
            
            logger.info(f"✅ Registered biometric client: {client_url}")
            return {
                'success': True,
                'message': 'Client registered successfully',
                'client_url': client_url
            }
        else:
            logger.error(f"❌ Failed to verify client at {client_url}")
            return {
                'success': False,
                'error': 'Client verification failed'
            }
    
    def list_active_clients(self) -> List[Dict]:
        """List all active biometric clients"""
        # This would typically query a database or cache
        # For now, return cached clients
        active_clients = []
        
        # In a real implementation, you'd store client registrations in database
        # and return them here with their status
        
        return active_clients

# Global instance
biometric_client_manager = BiometricClientManager()
