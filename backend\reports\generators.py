import os
import json
import csv
from io import Bytes<PERSON>, <PERSON><PERSON>
from datetime import datetime
from typing import Dict, Any, List
from decimal import Decimal

from django.conf import settings
from django.utils import timezone
from django.template.loader import render_to_string
from django.core.files.base import ContentFile

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.platypus.flowables import PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.linecharts import HorizontalLine<PERSON>hart
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

try:
    import openpyxl
    from openpyxl.styles import Font, <PERSON><PERSON><PERSON>, PatternFill, <PERSON>, Side
    from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False


class BaseReportGenerator:
    """Base class for report generators"""
    
    def __init__(self, tenant, report_data: Dict[str, Any]):
        self.tenant = tenant
        self.report_data = report_data
        self.generated_at = timezone.now()
    
    def generate(self, format_type: str) -> BytesIO:
        """Generate report in specified format"""
        if format_type == 'pdf':
            return self.generate_pdf()
        elif format_type == 'excel':
            return self.generate_excel()
        elif format_type == 'csv':
            return self.generate_csv()
        elif format_type == 'json':
            return self.generate_json()
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def generate_pdf(self) -> BytesIO:
        """Generate PDF report"""
        raise NotImplementedError("Subclasses must implement generate_pdf")
    
    def generate_excel(self) -> BytesIO:
        """Generate Excel report"""
        raise NotImplementedError("Subclasses must implement generate_excel")
    
    def generate_csv(self) -> BytesIO:
        """Generate CSV report"""
        raise NotImplementedError("Subclasses must implement generate_csv")
    
    def generate_json(self) -> BytesIO:
        """Generate JSON report"""
        output = BytesIO()
        json_data = {
            'report_metadata': {
                'tenant': self.tenant.name,
                'generated_at': self.generated_at.isoformat(),
                'report_type': getattr(self, 'report_type', 'unknown')
            },
            'data': self.report_data
        }
        output.write(json.dumps(json_data, indent=2, default=str).encode('utf-8'))
        output.seek(0)
        return output


class DemographicReportGenerator(BaseReportGenerator):
    """Generator for demographic analysis reports"""
    
    report_type = 'demographic'
    
    def generate_pdf(self) -> BytesIO:
        """Generate demographic PDF report"""
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        story.append(Paragraph(f"Demographic Analysis Report", title_style))
        story.append(Paragraph(f"Generated for: {self.tenant.name}", styles['Normal']))
        story.append(Paragraph(f"Generated on: {self.generated_at.strftime('%B %d, %Y at %I:%M %p')}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Summary section
        story.append(Paragraph("Executive Summary", styles['Heading2']))
        summary_data = [
            ['Total Citizens', str(self.report_data.get('total_citizens', 0))],
            ['Report Period', f"{self.report_data.get('filters_applied', {}).get('date_from', 'All time')} to {self.report_data.get('filters_applied', {}).get('date_to', 'Present')}"],
        ]
        
        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # Gender Distribution
        if self.report_data.get('gender_distribution'):
            story.append(Paragraph("Gender Distribution", styles['Heading2']))
            gender_data = [['Gender', 'Count', 'Percentage']]
            total = self.report_data.get('total_citizens', 1)
            
            for item in self.report_data['gender_distribution']:
                percentage = round((item['count'] / total) * 100, 1) if total > 0 else 0
                gender_data.append([
                    item.get('gender', 'Unknown').title(),
                    str(item['count']),
                    f"{percentage}%"
                ])
            
            gender_table = Table(gender_data, colWidths=[2*inch, 1.5*inch, 1.5*inch])
            gender_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(gender_table)
            story.append(Spacer(1, 20))
        
        # Age Group Distribution
        if self.report_data.get('age_group_distribution'):
            story.append(Paragraph("Age Group Distribution", styles['Heading2']))
            age_data = [['Age Group', 'Count', 'Percentage']]
            total = self.report_data.get('total_citizens', 1)
            
            for item in self.report_data['age_group_distribution']:
                percentage = round((item['count'] / total) * 100, 1) if total > 0 else 0
                age_data.append([
                    item.get('age_group', 'Unknown'),
                    str(item['count']),
                    f"{percentage}%"
                ])
            
            age_table = Table(age_data, colWidths=[2*inch, 1.5*inch, 1.5*inch])
            age_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(age_table)
            story.append(Spacer(1, 20))
        
        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph(f"Generated by GoID System - {self.tenant.name}", footer_style))
        
        doc.build(story)
        buffer.seek(0)
        return buffer
    
    def generate_excel(self) -> BytesIO:
        """Generate demographic Excel report"""
        if not EXCEL_AVAILABLE:
            raise ImportError("openpyxl is required for Excel generation")
        
        buffer = BytesIO()
        workbook = openpyxl.Workbook()
        
        # Remove default sheet
        workbook.remove(workbook.active)
        
        # Summary sheet
        summary_sheet = workbook.create_sheet("Summary")
        summary_sheet['A1'] = "Demographic Analysis Report"
        summary_sheet['A1'].font = Font(size=16, bold=True)
        summary_sheet['A3'] = f"Tenant: {self.tenant.name}"
        summary_sheet['A4'] = f"Generated: {self.generated_at.strftime('%Y-%m-%d %H:%M:%S')}"
        summary_sheet['A5'] = f"Total Citizens: {self.report_data.get('total_citizens', 0)}"
        
        # Gender Distribution sheet
        if self.report_data.get('gender_distribution'):
            gender_sheet = workbook.create_sheet("Gender Distribution")
            gender_sheet['A1'] = "Gender"
            gender_sheet['B1'] = "Count"
            gender_sheet['C1'] = "Percentage"
            
            # Style headers
            for cell in gender_sheet[1]:
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            total = self.report_data.get('total_citizens', 1)
            for idx, item in enumerate(self.report_data['gender_distribution'], 2):
                gender_sheet[f'A{idx}'] = item.get('gender', 'Unknown').title()
                gender_sheet[f'B{idx}'] = item['count']
                percentage = round((item['count'] / total) * 100, 1) if total > 0 else 0
                gender_sheet[f'C{idx}'] = f"{percentage}%"
        
        # Age Group Distribution sheet
        if self.report_data.get('age_group_distribution'):
            age_sheet = workbook.create_sheet("Age Groups")
            age_sheet['A1'] = "Age Group"
            age_sheet['B1'] = "Count"
            age_sheet['C1'] = "Percentage"
            
            # Style headers
            for cell in age_sheet[1]:
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            total = self.report_data.get('total_citizens', 1)
            for idx, item in enumerate(self.report_data['age_group_distribution'], 2):
                age_sheet[f'A{idx}'] = item.get('age_group', 'Unknown')
                age_sheet[f'B{idx}'] = item['count']
                percentage = round((item['count'] / total) * 100, 1) if total > 0 else 0
                age_sheet[f'C{idx}'] = f"{percentage}%"
        
        workbook.save(buffer)
        buffer.seek(0)
        return buffer
    
    def generate_csv(self) -> BytesIO:
        """Generate demographic CSV report"""
        output = StringIO()
        
        # Write header
        output.write(f"Demographic Analysis Report\n")
        output.write(f"Tenant: {self.tenant.name}\n")
        output.write(f"Generated: {self.generated_at.strftime('%Y-%m-%d %H:%M:%S')}\n")
        output.write(f"Total Citizens: {self.report_data.get('total_citizens', 0)}\n\n")
        
        # Gender distribution
        if self.report_data.get('gender_distribution'):
            output.write("Gender Distribution\n")
            output.write("Gender,Count,Percentage\n")
            total = self.report_data.get('total_citizens', 1)
            for item in self.report_data['gender_distribution']:
                percentage = round((item['count'] / total) * 100, 1) if total > 0 else 0
                output.write(f"{item.get('gender', 'Unknown').title()},{item['count']},{percentage}%\n")
            output.write("\n")
        
        # Age group distribution
        if self.report_data.get('age_group_distribution'):
            output.write("Age Group Distribution\n")
            output.write("Age Group,Count,Percentage\n")
            total = self.report_data.get('total_citizens', 1)
            for item in self.report_data['age_group_distribution']:
                percentage = round((item['count'] / total) * 100, 1) if total > 0 else 0
                output.write(f"{item.get('age_group', 'Unknown')},{item['count']},{percentage}%\n")
        
        # Convert to BytesIO
        buffer = BytesIO()
        buffer.write(output.getvalue().encode('utf-8'))
        buffer.seek(0)
        return buffer


class RegistrationTrendsReportGenerator(BaseReportGenerator):
    """Generator for registration trends reports"""

    report_type = 'registration_trends'

    def generate_pdf(self) -> BytesIO:
        """Generate registration trends PDF report"""
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []

        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )

        story.append(Paragraph("Registration Trends Report", title_style))
        story.append(Paragraph(f"Generated for: {self.tenant.name}", styles['Normal']))
        story.append(Paragraph(f"Generated on: {self.generated_at.strftime('%B %d, %Y at %I:%M %p')}", styles['Normal']))
        story.append(Spacer(1, 20))

        # Summary section
        story.append(Paragraph("Summary", styles['Heading2']))
        summary_data = [
            ['Total Registrations', str(self.report_data.get('total_registrations', 0))],
            ['Average Daily', str(self.report_data.get('average_daily', 0))],
            ['Period Start', self.report_data.get('period_start', 'N/A')],
            ['Period End', self.report_data.get('period_end', 'N/A')],
        ]

        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(summary_table)
        story.append(Spacer(1, 20))

        # Monthly trends
        if self.report_data.get('monthly_trends'):
            story.append(Paragraph("Monthly Registration Trends", styles['Heading2']))
            monthly_data = [['Month', 'Registrations']]

            for item in self.report_data['monthly_trends']:
                monthly_data.append([
                    item.get('month', 'Unknown'),
                    str(item['count'])
                ])

            monthly_table = Table(monthly_data, colWidths=[2*inch, 2*inch])
            monthly_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(monthly_table)
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph(f"Generated by GoID System - {self.tenant.name}", footer_style))

        doc.build(story)
        buffer.seek(0)
        return buffer

    def generate_excel(self) -> BytesIO:
        """Generate registration trends Excel report"""
        if not EXCEL_AVAILABLE:
            raise ImportError("openpyxl is required for Excel generation")

        buffer = BytesIO()
        workbook = openpyxl.Workbook()
        workbook.remove(workbook.active)

        # Summary sheet
        summary_sheet = workbook.create_sheet("Summary")
        summary_sheet['A1'] = "Registration Trends Report"
        summary_sheet['A1'].font = Font(size=16, bold=True)
        summary_sheet['A3'] = f"Tenant: {self.tenant.name}"
        summary_sheet['A4'] = f"Generated: {self.generated_at.strftime('%Y-%m-%d %H:%M:%S')}"
        summary_sheet['A5'] = f"Total Registrations: {self.report_data.get('total_registrations', 0)}"
        summary_sheet['A6'] = f"Average Daily: {self.report_data.get('average_daily', 0)}"

        # Monthly trends sheet
        if self.report_data.get('monthly_trends'):
            monthly_sheet = workbook.create_sheet("Monthly Trends")
            monthly_sheet['A1'] = "Month"
            monthly_sheet['B1'] = "Registrations"

            for cell in monthly_sheet[1]:
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            for idx, item in enumerate(self.report_data['monthly_trends'], 2):
                monthly_sheet[f'A{idx}'] = item.get('month', 'Unknown')
                monthly_sheet[f'B{idx}'] = item['count']

        # Daily trends sheet
        if self.report_data.get('daily_trends'):
            daily_sheet = workbook.create_sheet("Daily Trends")
            daily_sheet['A1'] = "Date"
            daily_sheet['B1'] = "Registrations"

            for cell in daily_sheet[1]:
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            for idx, item in enumerate(self.report_data['daily_trends'], 2):
                daily_sheet[f'A{idx}'] = item.get('day', 'Unknown')
                daily_sheet[f'B{idx}'] = item['count']

        workbook.save(buffer)
        buffer.seek(0)
        return buffer

    def generate_csv(self) -> BytesIO:
        """Generate registration trends CSV report"""
        output = StringIO()

        # Write header
        output.write(f"Registration Trends Report\n")
        output.write(f"Tenant: {self.tenant.name}\n")
        output.write(f"Generated: {self.generated_at.strftime('%Y-%m-%d %H:%M:%S')}\n")
        output.write(f"Total Registrations: {self.report_data.get('total_registrations', 0)}\n")
        output.write(f"Average Daily: {self.report_data.get('average_daily', 0)}\n\n")

        # Monthly trends
        if self.report_data.get('monthly_trends'):
            output.write("Monthly Trends\n")
            output.write("Month,Registrations\n")
            for item in self.report_data['monthly_trends']:
                output.write(f"{item.get('month', 'Unknown')},{item['count']}\n")
            output.write("\n")

        # Daily trends
        if self.report_data.get('daily_trends'):
            output.write("Daily Trends\n")
            output.write("Date,Registrations\n")
            for item in self.report_data['daily_trends']:
                output.write(f"{item.get('day', 'Unknown')},{item['count']}\n")

        # Convert to BytesIO
        buffer = BytesIO()
        buffer.write(output.getvalue().encode('utf-8'))
        buffer.seek(0)
        return buffer


class IDCardStatusReportGenerator(BaseReportGenerator):
    """Generator for ID card status reports"""

    report_type = 'id_card_status'

    def generate_pdf(self) -> BytesIO:
        """Generate ID card status PDF report"""
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []

        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )

        story.append(Paragraph("ID Card Status Report", title_style))
        story.append(Paragraph(f"Generated for: {self.tenant.name}", styles['Normal']))
        story.append(Paragraph(f"Generated on: {self.generated_at.strftime('%B %d, %Y at %I:%M %p')}", styles['Normal']))
        story.append(Spacer(1, 20))

        # Summary section
        story.append(Paragraph("Summary", styles['Heading2']))
        coverage_pct = self.report_data.get('coverage_percentage', 0)
        summary_data = [
            ['Total Citizens', str(self.report_data.get('total_citizens', 0))],
            ['Citizens with ID Cards', str(self.report_data.get('citizens_with_cards', 0))],
            ['Citizens without ID Cards', str(self.report_data.get('citizens_without_cards', 0))],
            ['Coverage Percentage', f"{coverage_pct}%"],
            ['Expiring Cards (30 days)', str(self.report_data.get('expiring_cards', 0))],
            ['Expired Cards', str(self.report_data.get('expired_cards', 0))],
        ]

        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(summary_table)
        story.append(Spacer(1, 20))

        # Status distribution
        if self.report_data.get('status_distribution'):
            story.append(Paragraph("Status Distribution", styles['Heading2']))
            status_data = [['Status', 'Count', 'Percentage']]
            total = self.report_data.get('total_id_cards', 1)

            for item in self.report_data['status_distribution']:
                percentage = round((item['count'] / total) * 100, 1) if total > 0 else 0
                status_data.append([
                    item.get('status', 'Unknown').replace('_', ' ').title(),
                    str(item['count']),
                    f"{percentage}%"
                ])

            status_table = Table(status_data, colWidths=[2*inch, 1.5*inch, 1.5*inch])
            status_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(status_table)

        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph(f"Generated by GoID System - {self.tenant.name}", footer_style))

        doc.build(story)
        buffer.seek(0)
        return buffer

    def generate_excel(self) -> BytesIO:
        """Generate ID card status Excel report"""
        if not EXCEL_AVAILABLE:
            raise ImportError("openpyxl is required for Excel generation")

        buffer = BytesIO()
        workbook = openpyxl.Workbook()
        workbook.remove(workbook.active)

        # Summary sheet
        summary_sheet = workbook.create_sheet("Summary")
        summary_sheet['A1'] = "ID Card Status Report"
        summary_sheet['A1'].font = Font(size=16, bold=True)
        summary_sheet['A3'] = f"Tenant: {self.tenant.name}"
        summary_sheet['A4'] = f"Generated: {self.generated_at.strftime('%Y-%m-%d %H:%M:%S')}"
        summary_sheet['A5'] = f"Total Citizens: {self.report_data.get('total_citizens', 0)}"
        summary_sheet['A6'] = f"Coverage: {self.report_data.get('coverage_percentage', 0)}%"

        # Status distribution sheet
        if self.report_data.get('status_distribution'):
            status_sheet = workbook.create_sheet("Status Distribution")
            status_sheet['A1'] = "Status"
            status_sheet['B1'] = "Count"
            status_sheet['C1'] = "Percentage"

            for cell in status_sheet[1]:
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            total = self.report_data.get('total_id_cards', 1)
            for idx, item in enumerate(self.report_data['status_distribution'], 2):
                status_sheet[f'A{idx}'] = item.get('status', 'Unknown').replace('_', ' ').title()
                status_sheet[f'B{idx}'] = item['count']
                percentage = round((item['count'] / total) * 100, 1) if total > 0 else 0
                status_sheet[f'C{idx}'] = f"{percentage}%"

        workbook.save(buffer)
        buffer.seek(0)
        return buffer

    def generate_csv(self) -> BytesIO:
        """Generate ID card status CSV report"""
        output = StringIO()

        # Write header
        output.write(f"ID Card Status Report\n")
        output.write(f"Tenant: {self.tenant.name}\n")
        output.write(f"Generated: {self.generated_at.strftime('%Y-%m-%d %H:%M:%S')}\n")
        output.write(f"Total Citizens: {self.report_data.get('total_citizens', 0)}\n")
        output.write(f"Coverage: {self.report_data.get('coverage_percentage', 0)}%\n\n")

        # Status distribution
        if self.report_data.get('status_distribution'):
            output.write("Status Distribution\n")
            output.write("Status,Count,Percentage\n")
            total = self.report_data.get('total_id_cards', 1)
            for item in self.report_data['status_distribution']:
                percentage = round((item['count'] / total) * 100, 1) if total > 0 else 0
                status = item.get('status', 'Unknown').replace('_', ' ').title()
                output.write(f"{status},{item['count']},{percentage}%\n")

        # Convert to BytesIO
        buffer = BytesIO()
        buffer.write(output.getvalue().encode('utf-8'))
        buffer.seek(0)
        return buffer


# Report generator factory
def get_report_generator(report_type: str, tenant, report_data: Dict[str, Any]) -> BaseReportGenerator:
    """Factory function to get appropriate report generator"""
    generators = {
        'demographic': DemographicReportGenerator,
        'registration_trends': RegistrationTrendsReportGenerator,
        'id_card_status': IDCardStatusReportGenerator,
    }

    generator_class = generators.get(report_type)
    if not generator_class:
        raise ValueError(f"No generator available for report type: {report_type}")

    return generator_class(tenant, report_data)
