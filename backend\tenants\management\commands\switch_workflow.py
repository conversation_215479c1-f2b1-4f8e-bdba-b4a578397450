from django.core.management.base import BaseCommand
from django.db import transaction
from tenants.models import Tenant, TenantWorkflowConfig
from users.models import User
from django_tenants.utils import schema_context


class Command(BaseCommand):
    help = 'Switch tenant workflow type (centralized, autonomous, hybrid)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=int,
            help='Specific tenant ID to configure'
        )
        parser.add_argument(
            '--tenant-name',
            type=str,
            help='Specific tenant name to configure'
        )
        parser.add_argument(
            '--workflow-type',
            type=str,
            choices=['centralized', 'autonomous', 'hybrid'],
            required=True,
            help='Target workflow type'
        )
        parser.add_argument(
            '--reason',
            type=str,
            default='Management command workflow switch',
            help='Reason for workflow change'
        )
        parser.add_argument(
            '--assign-printer',
            type=str,
            help='Email of user to assign as designated printer (for autonomous workflow)'
        )
        parser.add_argument(
            '--authorized-by',
            type=str,
            help='Email of subcity admin authorizing this change'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force workflow change without authorization checks'
        )

    def handle(self, *args, **options):
        tenant_id = options.get('tenant_id')
        tenant_name = options.get('tenant_name')
        workflow_type = options.get('workflow_type')
        reason = options.get('reason')
        printer_email = options.get('assign_printer')
        authorized_by = options.get('authorized_by')
        force = options.get('force')

        if not any([tenant_id, tenant_name]):
            self.stdout.write(
                self.style.ERROR('Please specify --tenant-id or --tenant-name')
            )
            return

        # Get target tenant
        try:
            if tenant_id:
                tenant = Tenant.objects.get(id=tenant_id)
            else:
                tenant = Tenant.objects.get(name=tenant_name)
        except Tenant.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Tenant not found')
            )
            return

        # Check authorization for kebele workflow changes
        if tenant.type == 'kebele' and not force:
            if not self.check_authorization(tenant, authorized_by):
                return

        self.stdout.write(f'🔄 Switching {tenant.name} to {workflow_type} workflow...')

        try:
            with transaction.atomic():
                # Get or create workflow configuration
                workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
                    tenant=tenant,
                    defaults={'workflow_type': 'centralized'}
                )

                old_workflow_type = workflow_config.workflow_type

                # Check if change is needed
                if old_workflow_type == workflow_type:
                    self.stdout.write(
                        self.style.WARNING(f'   ⚠️ Tenant is already using {workflow_type} workflow')
                    )
                    return

                # Apply new workflow configuration
                workflow_config.workflow_type = workflow_type
                
                if workflow_type == 'autonomous':
                    workflow_config.id_card_processing = self.get_autonomous_config()
                    workflow_config.citizen_registration = {
                        'full_workflow_local': True,
                        'requires_external_verification': False,
                        'biometric_processing': 'local',
                        'document_verification': 'local'
                    }
                    workflow_config.approval_workflow = {
                        'final_approval_level': 'kebele_leader',
                        'escalation_required': False,
                        'auto_approve_threshold': None,
                        'post_approval_printing': True
                    }
                elif workflow_type == 'centralized':
                    workflow_config.id_card_processing = self.get_centralized_config()
                    workflow_config.citizen_registration = {
                        'full_workflow_local': True,
                        'requires_external_verification': True,
                        'biometric_processing': 'local',
                        'document_verification': 'hierarchical'
                    }
                    workflow_config.approval_workflow = {
                        'final_approval_level': 'subcity_admin',
                        'escalation_required': True,
                        'auto_approve_threshold': None
                    }
                elif workflow_type == 'hybrid':
                    workflow_config.id_card_processing = self.get_hybrid_config()
                    workflow_config.citizen_registration = {
                        'full_workflow_local': True,
                        'requires_external_verification': False,
                        'biometric_processing': 'local',
                        'document_verification': 'local'
                    }
                    workflow_config.approval_workflow = {
                        'final_approval_level': 'kebele_leader',
                        'escalation_required': False,
                        'auto_approve_threshold': 95
                    }

                workflow_config.save()

                action = 'Created' if created else 'Updated'
                self.stdout.write(f'   ✅ {action} workflow configuration')
                self.stdout.write(f'   📋 Changed from: {old_workflow_type} → {workflow_type}')
                self.stdout.write(f'   📝 Reason: {reason}')

                # Assign designated printer if specified and workflow is autonomous
                if printer_email and workflow_type == 'autonomous':
                    self.assign_designated_printer(tenant, printer_email)

                # Show workflow impact
                self.show_workflow_impact(tenant, old_workflow_type, workflow_type)

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Error switching workflow: {e}')
            )

    def check_authorization(self, kebele_tenant, authorized_by_email):
        """Check if the workflow change is authorized by the appropriate subcity admin."""
        if not authorized_by_email:
            self.stdout.write(
                self.style.ERROR('   ❌ Kebele workflow changes require --authorized-by (subcity admin email)')
            )
            self.stdout.write('   💡 Use --force to bypass authorization checks')
            return False

        try:
            # Find the subcity that this kebele belongs to
            from tenants.models.kebele import Kebele
            from tenants.models.subcity import SubCity

            kebele = Kebele.objects.filter(tenant=kebele_tenant).first()
            if not kebele:
                self.stdout.write(
                    self.style.ERROR(f'   ❌ Kebele model not found for tenant {kebele_tenant.name}')
                )
                return False

            subcity = kebele.subcity

            # Check if the authorizing user is a subcity admin for this subcity
            authorizing_user = User.objects.filter(
                email=authorized_by_email,
                tenant=subcity.tenant,
                role='subcity_admin',
                is_active=True
            ).first()

            if not authorizing_user:
                self.stdout.write(
                    self.style.ERROR(f'   ❌ User {authorized_by_email} is not a subcity admin for {subcity.name}')
                )
                return False

            self.stdout.write(
                f'   ✅ Workflow change authorized by {authorizing_user.email} (Subcity Admin for {subcity.name})'
            )
            return True

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Error checking authorization: {e}')
            )
            return False

    def get_autonomous_config(self):
        """Get autonomous workflow configuration."""
        return {
            'can_print_locally': True,
            'requires_higher_approval': False,
            'approval_levels': ['kebele_leader'],
            'printing_authority': 'kebele',
            'quality_control': 'local',
            'granular_print_permissions': True,
            'print_roles': ['designated_printer', 'kebele_leader']
        }

    def get_centralized_config(self):
        """Get centralized workflow configuration."""
        return {
            'can_print_locally': False,
            'requires_higher_approval': True,
            'approval_levels': ['kebele_leader', 'subcity_admin'],
            'printing_authority': 'subcity',
            'quality_control': 'centralized'
        }

    def get_hybrid_config(self):
        """Get hybrid workflow configuration."""
        return {
            'can_print_locally': True,
            'requires_higher_approval': True,
            'approval_levels': ['kebele_leader'],
            'printing_authority': 'kebele',
            'quality_control': 'hybrid'
        }

    def assign_designated_printer(self, tenant, printer_email):
        """Assign a user as designated printer for the tenant."""
        try:
            with schema_context(tenant.schema_name):
                try:
                    user = User.objects.get(email=printer_email, tenant=tenant)
                except User.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f'   ❌ User {printer_email} not found in tenant {tenant.name}')
                    )
                    return

                # Add designated_printer to additional_roles
                additional_roles = getattr(user, 'additional_roles', [])
                if 'designated_printer' not in additional_roles:
                    additional_roles.append('designated_printer')
                    user.additional_roles = additional_roles
                    user.save()

                    self.stdout.write(
                        f'   ✅ Assigned {printer_email} as designated printer'
                    )
                else:
                    self.stdout.write(
                        f'   ℹ️ {printer_email} already has designated printer role'
                    )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Error assigning printer role: {e}')
            )

    def show_workflow_impact(self, tenant, old_workflow, new_workflow):
        """Show the impact of workflow change."""
        self.stdout.write(f'\n📊 Workflow Impact Analysis:')
        
        if new_workflow == 'autonomous':
            self.stdout.write(f'   🏢 Printing Location: Local kebele office')
            self.stdout.write(f'   ⚡ Processing Speed: 50-70% faster')
            self.stdout.write(f'   👥 Staff Training: Required')
            self.stdout.write(f'   🖨️ Equipment: ID card printer needed at kebele')
            self.stdout.write(f'   🔐 Security: Local quality control')
        elif new_workflow == 'centralized':
            self.stdout.write(f'   🏢 Printing Location: Subcity office')
            self.stdout.write(f'   ⚡ Processing Speed: Standard')
            self.stdout.write(f'   👥 Staff Training: Minimal')
            self.stdout.write(f'   🖨️ Equipment: Uses existing infrastructure')
            self.stdout.write(f'   🔐 Security: Centralized quality control')
        elif new_workflow == 'hybrid':
            self.stdout.write(f'   🏢 Printing Location: Local with oversight')
            self.stdout.write(f'   ⚡ Processing Speed: Moderately faster')
            self.stdout.write(f'   👥 Staff Training: Required')
            self.stdout.write(f'   🖨️ Equipment: Local printer + oversight system')
            self.stdout.write(f'   🔐 Security: Hybrid quality control')

        # Get current ID card statistics
        try:
            with schema_context(tenant.schema_name):
                from idcards.models import IDCard, IDCardStatus
                
                pending_cards = IDCard.objects.filter(
                    status__in=[IDCardStatus.PENDING_APPROVAL, IDCardStatus.KEBELE_APPROVED]
                ).count()
                
                approved_cards = IDCard.objects.filter(
                    status=IDCardStatus.APPROVED
                ).count()

                self.stdout.write(f'\n📈 Current State:')
                self.stdout.write(f'   📋 Pending Approvals: {pending_cards}')
                self.stdout.write(f'   ✅ Ready to Print: {approved_cards}')

        except Exception as e:
            self.stdout.write(f'   ⚠️ Could not get ID card statistics: {e}')

        self.stdout.write(f'\n✅ Workflow switch completed successfully!')
        self.stdout.write(f'💡 Next steps:')
        
        if new_workflow == 'autonomous':
            self.stdout.write(f'   1. Train kebele staff on approval process')
            self.stdout.write(f'   2. Install ID card printing equipment')
            self.stdout.write(f'   3. Assign designated printer users')
            self.stdout.write(f'   4. Test the complete workflow')
        elif new_workflow == 'centralized':
            self.stdout.write(f'   1. Coordinate with subcity office')
            self.stdout.write(f'   2. Update staff on submission procedures')
        elif new_workflow == 'hybrid':
            self.stdout.write(f'   1. Set up local printing equipment')
            self.stdout.write(f'   2. Establish oversight procedures')
            self.stdout.write(f'   3. Train staff on hybrid workflow')
