from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Report, ReportTemplate, ReportSchedule, ReportAccess, ReportType, ReportFormat

User = get_user_model()


class ReportTemplateSerializer(serializers.ModelSerializer):
    """Serializer for report templates"""
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    report_type_display = serializers.CharField(source='get_report_type_display', read_only=True)
    
    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'name', 'description', 'report_type', 'report_type_display',
            'template_config', 'default_filters', 'chart_configs',
            'created_by', 'created_by_username', 'created_at', 'updated_at',
            'is_active', 'is_system_template'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at', 'is_system_template']


class ReportSerializer(serializers.ModelSerializer):
    """Serializer for reports"""
    generated_by_username = serializers.Char<PERSON>ield(source='generated_by.username', read_only=True)
    report_type_display = serializers.CharField(source='get_report_type_display', read_only=True)
    format_display = serializers.CharField(source='get_format_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    file_url = serializers.SerializerMethodField()
    download_url = serializers.SerializerMethodField()
    is_downloadable = serializers.SerializerMethodField()
    processing_time_seconds = serializers.SerializerMethodField()
    
    class Meta:
        model = Report
        fields = [
            'id', 'title', 'description', 'report_type', 'report_type_display',
            'template', 'template_name', 'format', 'format_display',
            'filters', 'parameters', 'period_start', 'period_end',
            'status', 'status_display', 'generated_by', 'generated_by_username',
            'generated_at', 'file', 'file_url', 'download_url', 'file_size',
            'processing_time', 'processing_time_seconds', 'error_message',
            'created_at', 'updated_at', 'expires_at', 'total_records',
            'summary_data', 'is_downloadable', 'is_expired'
        ]
        read_only_fields = [
            'id', 'generated_by', 'generated_at', 'file', 'file_size',
            'processing_time', 'error_message', 'created_at', 'updated_at',
            'total_records', 'summary_data', 'is_expired'
        ]
    
    def get_file_url(self, obj):
        """Get file URL if available"""
        if obj.file:
            return obj.file.url
        return None
    
    def get_download_url(self, obj):
        """Get download URL for the report"""
        if obj.status == 'completed' and obj.file and not obj.is_expired:
            return f"/api/reports/{obj.id}/download/"
        return None
    
    def get_is_downloadable(self, obj):
        """Check if report is downloadable"""
        return (
            obj.status == 'completed' and 
            obj.file and 
            not obj.is_expired
        )
    
    def get_processing_time_seconds(self, obj):
        """Get processing time in seconds"""
        if obj.processing_time:
            return obj.processing_time.total_seconds()
        return None


class ReportCreateSerializer(serializers.Serializer):
    """Serializer for creating new reports"""
    title = serializers.CharField(max_length=300)
    description = serializers.CharField(required=False, allow_blank=True)
    report_type = serializers.ChoiceField(choices=ReportType.choices)
    template = serializers.PrimaryKeyRelatedField(
        queryset=ReportTemplate.objects.filter(is_active=True),
        required=False,
        allow_null=True
    )
    format = serializers.ChoiceField(choices=ReportFormat.choices)
    filters = serializers.JSONField(required=False, default=dict)
    parameters = serializers.JSONField(required=False, default=dict)
    period_start = serializers.DateTimeField(required=False, allow_null=True)
    period_end = serializers.DateTimeField(required=False, allow_null=True)
    
    def validate(self, data):
        """Validate report creation data"""
        # Validate period dates
        if data.get('period_start') and data.get('period_end'):
            if data['period_start'] >= data['period_end']:
                raise serializers.ValidationError(
                    "Period start must be before period end"
                )
        
        # Validate template matches report type
        if data.get('template') and data.get('report_type'):
            if data['template'].report_type != data['report_type']:
                raise serializers.ValidationError(
                    "Template report type must match selected report type"
                )
        
        return data


class ReportFilterSerializer(serializers.Serializer):
    """Serializer for report filtering"""
    report_type = serializers.ChoiceField(choices=ReportType.choices, required=False)
    format = serializers.ChoiceField(choices=ReportFormat.choices, required=False)
    status = serializers.CharField(required=False)
    date_from = serializers.DateTimeField(required=False)
    date_to = serializers.DateTimeField(required=False)
    generated_by = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        required=False
    )
    search = serializers.CharField(required=False, max_length=200)


class ReportScheduleSerializer(serializers.ModelSerializer):
    """Serializer for report schedules"""
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    format_display = serializers.CharField(source='get_format_display', read_only=True)
    
    class Meta:
        model = ReportSchedule
        fields = [
            'id', 'name', 'description', 'template', 'template_name',
            'format', 'format_display', 'cron_expression', 'timezone',
            'email_recipients', 'is_active', 'last_run', 'next_run',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_by', 'last_run', 'next_run', 'created_at', 'updated_at'
        ]
    
    def validate_cron_expression(self, value):
        """Validate cron expression format"""
        # Basic validation - in production, use a proper cron parser
        parts = value.split()
        if len(parts) != 5:
            raise serializers.ValidationError(
                "Cron expression must have 5 parts: minute hour day month day_of_week"
            )
        return value
    
    def validate_email_recipients(self, value):
        """Validate email recipients list"""
        if not isinstance(value, list):
            raise serializers.ValidationError("Email recipients must be a list")
        
        for email in value:
            if not isinstance(email, str) or '@' not in email:
                raise serializers.ValidationError(f"Invalid email address: {email}")
        
        return value


class ReportAccessSerializer(serializers.ModelSerializer):
    """Serializer for report access logs"""
    user_username = serializers.CharField(source='user.username', read_only=True)
    report_title = serializers.CharField(source='report.title', read_only=True)
    action_display = serializers.CharField(source='get_action_display', read_only=True)
    
    class Meta:
        model = ReportAccess
        fields = [
            'id', 'report', 'report_title', 'user', 'user_username',
            'accessed_at', 'ip_address', 'user_agent', 'action', 'action_display'
        ]
        read_only_fields = ['id', 'accessed_at']


class ReportSummarySerializer(serializers.Serializer):
    """Serializer for report summary statistics"""
    total_reports = serializers.IntegerField()
    reports_by_type = serializers.DictField()
    reports_by_status = serializers.DictField()
    reports_by_format = serializers.DictField()
    recent_reports = ReportSerializer(many=True)
    storage_usage = serializers.DictField()
    
    
class ReportAnalyticsSerializer(serializers.Serializer):
    """Serializer for report analytics data"""
    generation_trends = serializers.ListField()
    popular_report_types = serializers.ListField()
    user_activity = serializers.ListField()
    format_preferences = serializers.DictField()
    average_processing_time = serializers.FloatField()
    success_rate = serializers.FloatField()
