# 🚀 GoID Nginx Migration Guide

## Overview

This guide will help you migrate from the current port-based setup (3000/8000) to a professional nginx reverse proxy setup using port 80.

## 🎯 Benefits of Nginx Setup

### ✅ **Advantages:**
- **Single Port Access**: Everything accessible via port 80
- **Professional URLs**: No more port numbers in URLs
- **Tenant Routing Fix**: Resolves the Host header issues
- **Better Performance**: Nginx handles static files efficiently
- **SSL Ready**: Easy to add HTTPS later
- **Load Balancing**: Can scale to multiple backend instances
- **Security**: Better security headers and rate limiting

### 📊 **Before vs After:**

| Service | Before | After |
|---------|--------|-------|
| Frontend | http://************:3000 | http://************ |
| Backend API | http://************:8000/api/ | http://************/api/ |
| Django Admin | http://************:8000/admin/ | http://************/admin/ |
| PgAdmin | http://************:5050 | http://************:5050 |

## 🛠️ Migration Steps

### Step 1: Prepare Files
Upload these files to your server in the deployment directory:
- `nginx.conf` - Nginx configuration
- `docker-compose.nginx.yml` - Updated Docker Compose
- `deploy-nginx.sh` - Deployment script

### Step 2: Connect to Server
```bash
ssh administrator@************
cd goid-deployment
```

### Step 3: Run Migration
```bash
# Make the script executable
chmod +x deploy-nginx.sh

# Run the deployment
bash deploy-nginx.sh
```

### Step 4: Verify Deployment
```bash
# Check all services are running
docker-compose ps

# Test the main application
curl http://************/

# Test the API
curl http://************/api/common/language/info/

# Test admin panel
curl -I http://************/admin/
```

## 🔧 Architecture Changes

### **New Architecture:**
```
Internet → Port 80 → Nginx → {
    /api/* → Backend (Port 8000)
    /admin/* → Backend (Port 8000)
    /static/* → Backend (Port 8000)
    /media/* → Backend (Port 8000)
    /* → Frontend (Port 80)
}
```

### **Service Communication:**
- All services communicate internally via Docker network
- Only nginx exposes port 80 to the outside world
- Backend and frontend are not directly accessible from outside

## 📋 Configuration Details

### **Nginx Configuration Highlights:**
- **Upstream Servers**: Backend and frontend defined as upstreams
- **CORS Headers**: Properly configured for API access
- **Security Headers**: X-Frame-Options, X-Content-Type-Options, etc.
- **Static Files**: Efficient serving with caching
- **Health Check**: `/health` endpoint for monitoring

### **Backend Configuration:**
- **ALLOWED_HOSTS**: Includes nginx, backend, and domain names
- **Proxy Headers**: Configured to work behind reverse proxy
- **CORS**: Properly configured for frontend access

### **Frontend Configuration:**
- **API URL**: Changed to use port 80 (no port number needed)
- **Internal Communication**: Communicates with backend via nginx

## 🚨 Troubleshooting

### **Common Issues:**

#### 1. **502 Bad Gateway**
```bash
# Check if backend is running
docker-compose ps backend

# Check backend logs
docker-compose logs backend

# Restart backend
docker-compose restart backend
```

#### 2. **404 Not Found for API**
```bash
# Check nginx configuration
docker-compose exec nginx nginx -t

# Reload nginx configuration
docker-compose exec nginx nginx -s reload
```

#### 3. **CORS Errors**
```bash
# Check nginx logs
docker-compose logs nginx

# Verify CORS headers
curl -H "Origin: http://************" -I http://************/api/common/language/info/
```

#### 4. **Frontend Not Loading**
```bash
# Check frontend container
docker-compose ps frontend

# Check frontend logs
docker-compose logs frontend
```

### **Useful Commands:**
```bash
# View all logs
docker-compose logs -f

# Restart specific service
docker-compose restart [service_name]

# Check nginx configuration
docker-compose exec nginx nginx -t

# Reload nginx without restart
docker-compose exec nginx nginx -s reload

# Check service health
curl http://************/health
```

## 🔄 Rollback Plan

If you need to rollback to the previous setup:

```bash
# Stop nginx setup
docker-compose down

# Restore previous configuration
cp docker-compose.yml.backup.* docker-compose.yml

# Start previous setup
docker-compose up -d
```

## 🎯 Post-Migration Tasks

### **Immediate:**
1. ✅ Test all functionality through port 80
2. ✅ Verify API endpoints work without port numbers
3. ✅ Test frontend-backend communication
4. ✅ Confirm admin panel access

### **Future Enhancements:**
1. **SSL Setup**: Add HTTPS with Let's Encrypt
2. **Domain Configuration**: Point goid.uog.edu.et to server
3. **Monitoring**: Add nginx monitoring and logging
4. **Security**: Configure rate limiting and security rules
5. **Backup**: Set up automated backups

## 📞 Support

If you encounter any issues during migration:
1. Check the troubleshooting section above
2. Review the logs: `docker-compose logs -f`
3. Verify network connectivity: `curl http://************/health`

The migration should be seamless and resolve all the current API access issues while providing a more professional and scalable architecture.
