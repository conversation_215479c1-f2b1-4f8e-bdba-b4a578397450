#!/bin/bash

# Script to modify existing docker-compose.yml in goid-deployment folder
echo "🔧 Modifying Existing Docker Compose for Nginx"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found in current directory"
    print_error "Please run this script from the goid-deployment directory"
    exit 1
fi

print_success "Found docker-compose.yml in current directory"

# Show current content
print_status "Current docker-compose.yml content:"
echo "=================================="
cat docker-compose.yml
echo "=================================="

# Backup current file
BACKUP_FILE="docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)"
print_status "Creating backup: $BACKUP_FILE"
cp docker-compose.yml "$BACKUP_FILE"

# Check if nginx already exists
if grep -q "nginx:" docker-compose.yml; then
    print_warning "Nginx service already exists in docker-compose.yml"
    print_status "Current services:"
    docker-compose ps
    exit 0
fi

# Create nginx.conf if it doesn't exist
if [ ! -f "nginx.conf" ]; then
    print_status "Creating nginx.conf..."
    cat > nginx.conf << 'EOF'
upstream backend {
    server backend:8000;
}

upstream frontend {
    server frontend:80;
}

server {
    listen 80;
    server_name ************ goid.uog.edu.et localhost;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Increase client max body size for file uploads
    client_max_body_size 100M;
    
    # Backend API routes
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # CORS headers
        add_header Access-Control-Allow-Origin "http://************" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "http://************" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Django admin routes
    location /admin/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
    }
    
    # Django static files
    location /static/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Django media files
    location /media/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # Frontend routes (catch-all for React Router)
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle React Router (SPA)
        try_files $uri $uri/ @fallback;
    }
    
    # Fallback for React Router
    location @fallback {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
    print_success "Created nginx.conf"
fi

# Add nginx service to existing docker-compose.yml
print_status "Adding nginx service to docker-compose.yml..."

# Create a temporary file with the nginx service
cat > nginx_service.yml << 'EOF'

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - goid_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
EOF

# Modify the existing docker-compose.yml
# 1. Change frontend and backend ports to expose instead of ports
# 2. Add nginx service
# 3. Add nginx_logs volume

print_status "Modifying existing docker-compose.yml..."

# Create the modified docker-compose.yml
python3 << 'EOF'
import re
import sys

# Read the current docker-compose.yml
with open('docker-compose.yml', 'r') as f:
    content = f.read()

# Change frontend ports from "3000:80" to expose: ["80"]
content = re.sub(r'(\s+)ports:\s*\n\s*-\s*["\']?3000:80["\']?', r'\1expose:\n\1  - "80"', content)

# Change backend ports from "8000:8000" to expose: ["8000"] 
content = re.sub(r'(\s+)ports:\s*\n\s*-\s*["\']?8000:8000["\']?', r'\1expose:\n\1  - "8000"', content)

# Add nginx service before volumes section
volumes_match = re.search(r'\nvolumes:', content)
if volumes_match:
    nginx_service = '''
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - goid_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
'''
    content = content[:volumes_match.start()] + nginx_service + content[volumes_match.start():]

# Add nginx_logs volume
volumes_section = re.search(r'volumes:\s*\n((?:\s+\w+:\s*\n?)*)', content)
if volumes_section:
    existing_volumes = volumes_section.group(1)
    if 'nginx_logs:' not in existing_volumes:
        new_volumes = existing_volumes + '  nginx_logs:\n'
        content = content[:volumes_section.start(1)] + new_volumes + content[volumes_section.end(1):]

# Write the modified content
with open('docker-compose.yml', 'w') as f:
    f.write(content)

print("Modified docker-compose.yml successfully")
EOF

print_success "Modified docker-compose.yml with nginx service"

# Show the modified content
print_status "Modified docker-compose.yml content:"
echo "=================================="
cat docker-compose.yml
echo "=================================="

# Deploy the changes
print_status "Deploying the modified configuration..."
docker-compose down
sleep 5
docker-compose up -d

# Wait for services to start
print_status "Waiting for services to start..."
sleep 30

# Test the deployment
print_status "Testing the deployment..."
docker-compose ps

print_status "Testing connectivity..."
echo "Port 80: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:80 2>/dev/null || echo 'Connection refused')"
echo "API: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/api/common/language/info/ 2>/dev/null || echo 'Failed')"
echo "Frontend: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/ 2>/dev/null || echo 'Failed')"

print_success "Deployment completed!"
print_status "Your application is now available at:"
print_status "  Main App: http://************"
print_status "  API: http://************/api/"
print_status "  Admin: http://************/admin/"

print_status "Backup saved as: $BACKUP_FILE"
EOF
