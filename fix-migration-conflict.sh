#!/bin/bash

# Quick fix for fingerprint migration conflict
# Run this script to resolve the migration issue

echo "🔧 Fixing fingerprint migration conflict..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found. Please run this script from the deployment directory."
    exit 1
fi

print_status "Found docker-compose.yml ✅"

# Check if containers are running
if ! docker-compose ps | grep -q "Up"; then
    print_warning "Containers don't seem to be running. Starting them..."
    docker-compose up -d
    sleep 10
fi

print_status "Checking database for existing fingerprint columns..."

# Check if fingerprint columns exist in database
FINGERPRINT_COLUMNS=$(docker-compose exec -T db psql -U postgres -d goid_db -t -c "
SELECT column_name 
FROM information_schema.columns 
WHERE table_name='citizens_citizen' 
AND column_name IN ('fingerprint_option', 'no_fingerprint_reason');
" 2>/dev/null | tr -d ' ' | grep -v '^$' | wc -l)

if [ "$FINGERPRINT_COLUMNS" -eq 2 ]; then
    print_success "Fingerprint columns already exist in database"
    
    # Remove the problematic migration record
    print_status "Removing problematic migration record..."
    docker-compose exec -T db psql -U postgres -d goid_db -c "
    DELETE FROM django_migrations 
    WHERE app='citizens' AND name='0006_citizen_fingerprint_option_and_more';
    " >/dev/null 2>&1
    
    print_success "Problematic migration record removed"
    
    # Mark our safe migration as applied
    print_status "Marking safe migration as applied..."
    docker-compose exec -T db psql -U postgres -d goid_db -c "
    INSERT INTO django_migrations (app, name, applied) 
    VALUES ('citizens', '0006_add_fingerprint_option_safe', NOW())
    ON CONFLICT (app, name) DO NOTHING;
    " >/dev/null 2>&1
    
    print_success "Safe migration marked as applied"
    
elif [ "$FINGERPRINT_COLUMNS" -eq 0 ]; then
    print_warning "Fingerprint columns don't exist. They will be created by migration."
else
    print_warning "Only $FINGERPRINT_COLUMNS fingerprint column(s) found. Expected 2."
fi

# Try to run migrations
print_status "Running database migrations..."
if docker-compose exec backend python manage.py migrate --fake-initial; then
    print_success "Migrations completed successfully"
else
    print_error "Migration failed. Trying alternative approach..."
    
    # Try to fake the problematic migration
    print_status "Attempting to fake problematic migration..."
    docker-compose exec backend python manage.py migrate citizens 0006_citizen_fingerprint_option_and_more --fake 2>/dev/null || true
    
    # Run migrations again
    if docker-compose exec backend python manage.py migrate; then
        print_success "Migrations completed on second attempt"
    else
        print_error "Migrations still failing. Manual intervention may be required."
        exit 1
    fi
fi

# Restart containers to ensure everything is working
print_status "Restarting containers to apply changes..."
docker-compose restart backend

# Wait for backend to be ready
print_status "Waiting for backend to be ready..."
sleep 15

# Test if the application is responding
print_status "Testing application health..."
if curl -f http://localhost:8000/api/health/ > /dev/null 2>&1; then
    print_success "Application is responding ✅"
elif curl -f http://localhost/api/health/ > /dev/null 2>&1; then
    print_success "Application is responding ✅"
else
    print_warning "Application health check failed - this might be normal during startup"
fi

# Final verification
print_status "Verifying fingerprint columns..."
FINAL_COLUMNS=$(docker-compose exec -T db psql -U postgres -d goid_db -t -c "
SELECT column_name 
FROM information_schema.columns 
WHERE table_name='citizens_citizen' 
AND column_name IN ('fingerprint_option', 'no_fingerprint_reason');
" 2>/dev/null | tr -d ' ' | grep -v '^$' | wc -l)

if [ "$FINAL_COLUMNS" -eq 2 ]; then
    print_success "✅ Fingerprint columns verified in database"
else
    print_error "❌ Fingerprint columns verification failed"
    exit 1
fi

echo ""
print_success "🎉 Migration conflict resolved!"
echo ""
print_status "📋 What was fixed:"
print_status "  ✅ Removed problematic migration record"
print_status "  ✅ Ensured fingerprint columns exist"
print_status "  ✅ Applied safe migration approach"
print_status "  ✅ Restarted services"
echo ""
print_status "🧪 Test the fix:"
print_status "  1. Go to citizen registration"
print_status "  2. Navigate to biometric step"
print_status "  3. Test fingerprint options"
echo ""
print_success "The fingerprint feature should now work correctly!"
