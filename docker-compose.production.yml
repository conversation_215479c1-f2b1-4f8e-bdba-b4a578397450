version: '3.8'

services:
  # Main web application
  backend:
    image: aragawmebratu/goid-production:backend-${BACKEND_VERSION:-latest}
    # Uncomment below for local development builds:
    # build:
    #   context: ./backend
    #   dockerfile: Dockerfile.production
    ports:
      - "8000:8000"
    environment:
      - DJANGO_SETTINGS_MODULE=goid.settings
      - DEBUG=False
      - DATABASE_URL=postgresql://goid_user:${DB_PASSWORD:-goid_password}@db:5432/goid_db
      - REDIS_URL=redis://redis:6379/0
      - ALLOWED_HOSTS=*
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      # Backend can also access external biometric service if needed
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - ./backend/media:/app/media
      - ./backend/static:/app/static
    networks:
      - goid_network
    extra_hosts:
      # Allow container to access host services for external biometric service
      - "host.docker.internal:host-gateway"
    restart: unless-stopped

  # Frontend (for admin interface)
  frontend:
    image: aragawmebratu/goid-frontend:frontend-${FRONTEND_VERSION:-latest}
    # Uncomment below for local development builds:
    # build:
    #   context: ./frontend
    #   dockerfile: Dockerfile.production
    #   args:
    #     - VITE_API_URL=http://************
    ports:
      - "3000:80"
      # Frontend will automatically detect external biometric service
      # running on host machine at various addresses
    depends_on:
      - backend
    networks:
      - goid_network
    extra_hosts:
      # Allow container to access host services for external biometric service
      - "host.docker.internal:host-gateway"
    restart: unless-stopped

  # Database
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: goid_db
      POSTGRES_USER: goid_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-goid_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - goid_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U goid_user -d goid_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - goid_network
    restart: unless-stopped

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - db
    networks:
      - goid_network
    restart: unless-stopped



volumes:
  postgres_data:
  redis_data:
  pgadmin_data:

networks:
  goid_network:
    driver: bridge

# ========================================
# EXTERNAL BIOMETRIC SERVICE SETUP
# ========================================
#
# For fingerprint capture functionality, you need to run the external
# biometric service on the host machine for direct USB device access.
#
# SETUP INSTRUCTIONS:
#
# 1. Install Python dependencies for biometric service:
#    > cd local-biometric-service
#    > pip install -r requirements.txt
#
# 2. Ensure your Futronic FS88H device is connected via USB
#
# 3. Start the external biometric service:
#    > start_external_biometric_service.bat
#    OR manually:
#    > cd local-biometric-service
#    > python working_fingerprint_service.py
#
# 4. Start the production Docker services:
#    > docker-compose -f docker-compose.production.yml up -d
#
# 5. The frontend will automatically detect the external biometric service
#    running on the host at http://localhost:8001 and use it for fingerprint
#    capture. If the external service is not available, it will fall back
#    to server-side biometric processing.
#
# SERVICE ENDPOINTS:
# - Health Check: http://localhost:8001/api/health
# - Device Status: http://localhost:8001/api/device/status
# - Capture: http://localhost:8001/api/capture/fingerprint
#
# TROUBLESHOOTING:
# - Ensure Windows Firewall allows Python to accept connections
# - Check that the Futronic device drivers are installed
# - Verify the device is detected in Device Manager
# - Keep the biometric service window open while using the system
