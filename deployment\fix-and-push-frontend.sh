#!/bin/bash

# <PERSON>ript to fix frontend API configuration and push to Docker Hub
echo "🔧 Fixing Frontend API Configuration and Pushing to Docker Hub"
echo "============================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
DOCKER_HUB_USERNAME="aragawmebratu"
IMAGE_NAME="goid-production"
FRONTEND_TAG="frontend-nginx-fixed"
API_URL="http://************"

print_status "Configuration:"
print_status "  Docker Hub: $DOCKER_HUB_USERNAME"
print_status "  Image: $IMAGE_NAME"
print_status "  Tag: $FRONTEND_TAG"
print_status "  API URL: $API_URL"
echo ""

# Check if we're in the right directory
if [ ! -f "frontend/package.json" ]; then
    print_error "frontend/package.json not found"
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_success "Found frontend directory"

# Show current frontend configuration
print_status "Current frontend API configuration:"
echo "======================================"
echo "📁 frontend/public/config.js:"
cat frontend/public/config.js
echo ""
echo "📁 frontend/.env.production:"
cat frontend/.env.production
echo ""
echo "📁 frontend/src/utils/axios.js (API URL detection):"
grep -A 10 -B 2 "getApiUrl" frontend/src/utils/axios.js
echo "======================================"
echo ""

# Verify the changes are correct
print_status "Verifying API URL configuration..."
if grep -q "http://************:8000" frontend/public/config.js; then
    print_error "❌ frontend/public/config.js still contains :8000"
    exit 1
fi

if grep -q "http://************:8000" frontend/.env.production; then
    print_error "❌ frontend/.env.production still contains :8000"
    exit 1
fi

if grep -q "localhost:8000" frontend/src/utils/axios.js; then
    print_error "❌ frontend/src/utils/axios.js still contains localhost:8000"
    exit 1
fi

print_success "✅ All API URL configurations updated correctly"

# Ask for confirmation
echo ""
read -p "🚀 Build and push frontend image with nginx-compatible API URLs? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Build cancelled"
    exit 0
fi

# Build the frontend image
print_status "Building frontend Docker image..."
cd frontend

# Build with correct API URL
docker build \
    -f Dockerfile.production \
    --build-arg VITE_API_URL="$API_URL" \
    --build-arg VITE_BIOMETRIC_SERVICE_URL="http://localhost:8002" \
    -t "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG" \
    .

if [ $? -eq 0 ]; then
    print_success "✅ Frontend image built successfully"
else
    print_error "❌ Failed to build frontend image"
    exit 1
fi

# Tag as latest as well
docker tag "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG" "$DOCKER_HUB_USERNAME/$IMAGE_NAME:frontend-latest"

print_success "✅ Tagged image as frontend-latest"

# Push to Docker Hub
print_status "Pushing images to Docker Hub..."

# Push the specific tag
docker push "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"
if [ $? -eq 0 ]; then
    print_success "✅ Pushed $FRONTEND_TAG"
else
    print_error "❌ Failed to push $FRONTEND_TAG"
    exit 1
fi

# Push the latest tag
docker push "$DOCKER_HUB_USERNAME/$IMAGE_NAME:frontend-latest"
if [ $? -eq 0 ]; then
    print_success "✅ Pushed frontend-latest"
else
    print_error "❌ Failed to push frontend-latest"
    exit 1
fi

cd ..

print_success "🎉 Frontend image successfully built and pushed!"
echo ""
print_status "📋 Summary of changes:"
print_status "  ✅ Updated API URL from http://************:8000 to http://************"
print_status "  ✅ Fixed axios.js to use nginx reverse proxy (no port 8000)"
print_status "  ✅ Updated runtime config.js for nginx compatibility"
print_status "  ✅ Built and pushed Docker image with nginx-compatible configuration"
echo ""
print_status "🚀 Next steps:"
print_status "  1. Update your docker-compose.yml to use the new image:"
print_status "     image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"
print_status "  2. Restart the frontend service:"
print_status "     docker-compose pull frontend && docker-compose up -d frontend"
print_status "  3. Test the API calls - they should now work without :8000 errors"
echo ""
print_status "🔗 Docker Hub images:"
print_status "  📦 $DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"
print_status "  📦 $DOCKER_HUB_USERNAME/$IMAGE_NAME:frontend-latest"

echo ""
print_success "Frontend is now configured for nginx reverse proxy on port 80!"
