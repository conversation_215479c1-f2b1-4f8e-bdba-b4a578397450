# Generated by Django 4.2.7

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('report_type', models.CharField(choices=[('demographic', 'Demographic Analysis'), ('registration_trends', 'Registration Trends'), ('id_card_status', 'ID Card Status'), ('service_access', 'Service Access Analytics'), ('socioeconomic_impact', 'Socioeconomic Impact')], max_length=50)),
                ('template_config', models.J<PERSON><PERSON>ield(default=dict, help_text='Configuration for report generation')),
                ('default_filters', models.J<PERSON><PERSON>ield(default=dict, help_text='Default filters for this template')),
                ('chart_configs', models.J<PERSON>NField(default=list, help_text='Chart configurations')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_system_template', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=300)),
                ('description', models.TextField(blank=True)),
                ('report_type', models.CharField(choices=[('demographic', 'Demographic Analysis'), ('registration_trends', 'Registration Trends'), ('id_card_status', 'ID Card Status'), ('service_access', 'Service Access Analytics'), ('socioeconomic_impact', 'Socioeconomic Impact')], max_length=50)),
                ('format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('json', 'JSON')], max_length=20)),
                ('filters', models.JSONField(default=dict, help_text='Filters applied during report generation')),
                ('parameters', models.JSONField(default=dict, help_text='Additional parameters for report generation')),
                ('period_start', models.DateTimeField(blank=True, null=True)),
                ('period_end', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('file', models.FileField(blank=True, null=True, upload_to='reports/%Y/%m/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'xlsx', 'csv', 'json'])])),
                ('file_size', models.PositiveIntegerField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('metadata', models.JSONField(default=dict, help_text='Additional metadata about the report')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='generated_reports', to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='reports.reporttemplate')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportSchedule',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('cron_expression', models.CharField(help_text='Cron expression for scheduling (e.g., 0 9 * * 1 for every Monday at 9 AM)', max_length=100)),
                ('email_recipients', models.JSONField(default=list, help_text='List of email addresses to send reports to')),
                ('is_active', models.BooleanField(default=True)),
                ('last_run', models.DateTimeField(blank=True, null=True)),
                ('next_run', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reports.reporttemplate')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ReportAccess',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('accessed_at', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('action', models.CharField(choices=[('view', 'Viewed'), ('download', 'Downloaded'), ('share', 'Shared')], max_length=50)),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='access_logs', to='reports.report')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-accessed_at'],
            },
        ),
        migrations.AddIndex(
            model_name='reportaccess',
            index=models.Index(fields=['report', 'user'], name='reports_rep_report__b8e7a8_idx'),
        ),
        migrations.AddIndex(
            model_name='reportaccess',
            index=models.Index(fields=['accessed_at'], name='reports_rep_accesse_4b8c5a_idx'),
        ),
        migrations.AddIndex(
            model_name='report',
            index=models.Index(fields=['status'], name='reports_rep_status_8b4c5a_idx'),
        ),
        migrations.AddIndex(
            model_name='report',
            index=models.Index(fields=['report_type'], name='reports_rep_report__a1b2c3_idx'),
        ),
        migrations.AddIndex(
            model_name='report',
            index=models.Index(fields=['created_at'], name='reports_rep_created_d4e5f6_idx'),
        ),
    ]
