#!/usr/bin/env python3
"""
Diagnostic script to check photo processing functionality
Run this on Windows development environment
"""

import os
import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if all required dependencies are available"""
    print("=" * 60)
    print("CHECKING PHOTO PROCESSING DEPENDENCIES")
    print("=" * 60)
    
    dependencies = {
        'PIL': 'Pillow',
        'rembg': 'rembg',
        'cv2': 'opencv-python-headless',
        'numpy': 'numpy'
    }
    
    results = {}
    
    for module, package in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {package}: Available")
            results[module] = True
        except ImportError as e:
            print(f"❌ {package}: Missing - {e}")
            results[module] = False
    
    return results

def check_rembg_functionality():
    """Test rembg background removal functionality"""
    print("\n" + "=" * 60)
    print("TESTING REMBG FUNCTIONALITY")
    print("=" * 60)
    
    try:
        from rembg import remove, new_session
        print("✅ rembg imported successfully")
        
        # Test session creation
        try:
            session = new_session('u2net')
            print("✅ u2net model session created successfully")
            print(f"   Session type: {type(session)}")
            return True
        except Exception as e:
            print(f"❌ Failed to create u2net session: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ rembg import failed: {e}")
        return False

def test_photo_processor():
    """Test the PhotoProcessor class"""
    print("\n" + "=" * 60)
    print("TESTING PHOTO PROCESSOR CLASS")
    print("=" * 60)
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from idcards.photo_processing import PhotoProcessor, REMBG_AVAILABLE, CV2_AVAILABLE
        
        print(f"✅ PhotoProcessor imported successfully")
        print(f"   REMBG_AVAILABLE: {REMBG_AVAILABLE}")
        print(f"   CV2_AVAILABLE: {CV2_AVAILABLE}")
        
        # Create processor instance
        processor = PhotoProcessor()
        print(f"   Processor session: {processor.session is not None}")
        
        return processor
        
    except Exception as e:
        print(f"❌ PhotoProcessor test failed: {e}")
        return None

def test_with_sample_image():
    """Test background removal with a sample image"""
    print("\n" + "=" * 60)
    print("TESTING WITH SAMPLE IMAGE")
    print("=" * 60)
    
    try:
        from PIL import Image
        import io
        import base64
        
        # Create a simple test image (100x100 red square)
        test_image = Image.new('RGB', (100, 100), color='red')
        
        # Convert to bytes
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='PNG')
        img_bytes = img_buffer.getvalue()
        
        print("✅ Test image created (100x100 red square)")
        
        # Test with PhotoProcessor
        processor = test_photo_processor()
        if processor:
            try:
                result = processor.process_id_photo(img_bytes, remove_bg=True, enhance=True)
                print(f"✅ Photo processing completed")
                print(f"   Success: {result.get('success', False)}")
                print(f"   Background removed: {result.get('background_removed', False)}")
                print(f"   Enhanced: {result.get('enhanced', False)}")
                print(f"   REMBG available: {result.get('rembg_available', False)}")
                
                if result.get('error'):
                    print(f"   Error: {result['error']}")
                    
                return result
                
            except Exception as e:
                print(f"❌ Photo processing failed: {e}")
                return None
        
    except Exception as e:
        print(f"❌ Sample image test failed: {e}")
        return None

def check_model_files():
    """Check if rembg model files are downloaded"""
    print("\n" + "=" * 60)
    print("CHECKING REMBG MODEL FILES")
    print("=" * 60)
    
    try:
        import rembg
        from pathlib import Path
        
        # Common locations for rembg models
        possible_locations = [
            Path.home() / '.u2net',
            Path.home() / '.cache' / 'rembg',
            Path(rembg.__file__).parent / 'models',
        ]
        
        for location in possible_locations:
            if location.exists():
                print(f"✅ Found model directory: {location}")
                model_files = list(location.glob('*.pth')) + list(location.glob('*.onnx'))
                for model_file in model_files:
                    size_mb = model_file.stat().st_size / (1024 * 1024)
                    print(f"   📁 {model_file.name} ({size_mb:.1f} MB)")
            else:
                print(f"❌ Model directory not found: {location}")
                
    except Exception as e:
        print(f"❌ Model file check failed: {e}")

def main():
    """Main diagnostic function"""
    print("GoID Photo Processing Diagnostic Tool")
    print("Running on:", sys.platform)
    print("Python version:", sys.version)
    print()
    
    # Check dependencies
    deps = check_dependencies()
    
    # Check rembg functionality
    rembg_ok = check_rembg_functionality()
    
    # Check model files
    check_model_files()
    
    # Test PhotoProcessor
    processor = test_photo_processor()
    
    # Test with sample image
    if processor:
        test_with_sample_image()
    
    print("\n" + "=" * 60)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    if all(deps.values()) and rembg_ok and processor:
        print("✅ Photo processing system is working correctly")
        print("   Background removal should be functional")
    else:
        print("❌ Photo processing system has issues")
        print("   Background removal may not work properly")
        
        if not deps.get('rembg', False):
            print("   → Install rembg: pip install rembg")
        if not deps.get('cv2', False):
            print("   → Install OpenCV: pip install opencv-python-headless")
        if not rembg_ok:
            print("   → Check rembg model download")

if __name__ == '__main__':
    main()
