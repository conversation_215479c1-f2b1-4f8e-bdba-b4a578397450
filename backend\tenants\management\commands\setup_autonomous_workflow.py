from django.core.management.base import BaseCommand
from django.db import transaction
from tenants.models import Tenant, TenantWorkflowConfig
from users.models import User, UserRole
from django_tenants.utils import schema_context


class Command(BaseCommand):
    help = 'Setup full autonomous workflow for kebeles with granular printing permissions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=int,
            help='Specific tenant ID to configure (optional)'
        )
        parser.add_argument(
            '--tenant-name',
            type=str,
            help='Specific tenant name to configure (optional)'
        )
        parser.add_argument(
            '--all-kebeles',
            action='store_true',
            help='Configure all kebele tenants'
        )
        parser.add_argument(
            '--assign-printer',
            type=str,
            help='Email of user to assign as designated printer'
        )

    def handle(self, *args, **options):
        tenant_id = options.get('tenant_id')
        tenant_name = options.get('tenant_name')
        all_kebeles = options.get('all_kebeles')
        printer_email = options.get('assign_printer')

        if not any([tenant_id, tenant_name, all_kebeles]):
            self.stdout.write(
                self.style.ERROR('Please specify --tenant-id, --tenant-name, or --all-kebeles')
            )
            return

        # Get target tenants
        tenants = []
        if tenant_id:
            try:
                tenants = [Tenant.objects.get(id=tenant_id)]
            except Tenant.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Tenant with ID {tenant_id} not found'))
                return
        elif tenant_name:
            try:
                tenants = [Tenant.objects.get(name=tenant_name)]
            except Tenant.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Tenant with name {tenant_name} not found'))
                return
        elif all_kebeles:
            tenants = Tenant.objects.filter(type='kebele')

        if not tenants:
            self.stdout.write(self.style.ERROR('No tenants found to configure'))
            return

        self.stdout.write(f'Configuring {len(tenants)} tenant(s) for full autonomous workflow...')

        for tenant in tenants:
            self.configure_tenant_workflow(tenant, printer_email)

        self.stdout.write(self.style.SUCCESS('✅ Full autonomous workflow configuration completed'))

    def configure_tenant_workflow(self, tenant, printer_email=None):
        """Configure a single tenant for full autonomous workflow."""
        self.stdout.write(f'\n🔧 Configuring tenant: {tenant.name} ({tenant.type})')

        if tenant.type != 'kebele':
            self.stdout.write(
                self.style.WARNING(f'   ⚠️ Skipping {tenant.name} - only kebele tenants supported')
            )
            return

        try:
            with transaction.atomic():
                # Create or update workflow configuration
                config, created = TenantWorkflowConfig.objects.get_or_create(
                    tenant=tenant,
                    defaults={
                        'workflow_type': 'autonomous',
                        'id_card_processing': {
                            'can_print_locally': True,
                            'requires_higher_approval': False,
                            'approval_levels': ['kebele_leader'],
                            'printing_authority': 'kebele',
                            'quality_control': 'local',
                            'granular_print_permissions': True,
                            'print_roles': ['designated_printer', 'kebele_leader']
                        },
                        'citizen_registration': {
                            'full_workflow_local': True,
                            'requires_external_verification': False,
                            'biometric_processing': 'local',
                            'document_verification': 'local'
                        },
                        'approval_workflow': {
                            'final_approval_level': 'kebele_leader',
                            'escalation_required': False,
                            'auto_approve_threshold': None,
                            'post_approval_printing': True
                        }
                    }
                )

                if not created:
                    # Update existing configuration
                    config.workflow_type = 'autonomous'
                    config.id_card_processing = {
                        'can_print_locally': True,
                        'requires_higher_approval': False,
                        'approval_levels': ['kebele_leader'],
                        'printing_authority': 'kebele',
                        'quality_control': 'local',
                        'granular_print_permissions': True,
                        'print_roles': ['designated_printer', 'kebele_leader']
                    }
                    config.save()

                action = 'Created' if created else 'Updated'
                self.stdout.write(f'   ✅ {action} workflow configuration')

                # Assign designated printer if specified
                if printer_email:
                    self.assign_designated_printer(tenant, printer_email)

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Error configuring {tenant.name}: {e}')
            )

    def assign_designated_printer(self, tenant, printer_email):
        """Assign a user as designated printer for the tenant."""
        try:
            with schema_context(tenant.schema_name):
                # Find user in tenant schema
                try:
                    user = User.objects.get(email=printer_email, tenant=tenant)
                except User.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f'   ❌ User {printer_email} not found in tenant {tenant.name}')
                    )
                    return

                # Add designated_printer to additional_roles
                additional_roles = getattr(user, 'additional_roles', [])
                if 'designated_printer' not in additional_roles:
                    additional_roles.append('designated_printer')
                    user.additional_roles = additional_roles
                    user.save()

                    self.stdout.write(
                        f'   ✅ Assigned {printer_email} as designated printer'
                    )
                else:
                    self.stdout.write(
                        f'   ℹ️ {printer_email} already has designated printer role'
                    )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Error assigning printer role: {e}')
            )
