#!/bin/bash

# GoID Backend Troubleshooting Script
echo "🔍 GoID Backend Troubleshooting Script"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker and Docker Compose are available
print_status "Checking Docker installation..."
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose is not installed or not in PATH"
    exit 1
fi

print_success "Docker and Docker Compose are available"

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found in current directory"
    print_status "Current directory: $(pwd)"
    print_status "Files in directory:"
    ls -la
    exit 1
fi

print_success "docker-compose.yml found"

# Show current Docker Compose services status
print_status "Checking Docker Compose services status..."
docker-compose ps

# Check if backend container is running
print_status "Checking backend container status..."
BACKEND_CONTAINER=$(docker-compose ps -q backend)
if [ -z "$BACKEND_CONTAINER" ]; then
    print_error "Backend container is not running"
    print_status "Attempting to start services..."
    docker-compose up -d
    sleep 10
    BACKEND_CONTAINER=$(docker-compose ps -q backend)
fi

if [ -n "$BACKEND_CONTAINER" ]; then
    print_success "Backend container is running: $BACKEND_CONTAINER"
    
    # Check container health
    print_status "Checking container health..."
    docker inspect $BACKEND_CONTAINER --format='{{.State.Health.Status}}' 2>/dev/null || echo "No health check configured"
    
    # Check container logs
    print_status "Recent backend container logs:"
    docker-compose logs --tail=20 backend
    
    # Check if backend is responding inside container
    print_status "Testing backend connectivity inside container..."
    docker exec $BACKEND_CONTAINER curl -f http://localhost:8000/api/health/ 2>/dev/null && print_success "Backend responds inside container" || print_warning "Backend not responding inside container"
    
else
    print_error "Backend container failed to start"
    print_status "Backend container logs:"
    docker-compose logs backend
    exit 1
fi

# Check network connectivity
print_status "Checking network connectivity..."

# Test from host to container
print_status "Testing backend connectivity from host..."
curl -f http://localhost:8000/api/health/ 2>/dev/null && print_success "Backend accessible from host" || print_warning "Backend not accessible from host"

# Test with server IP
SERVER_IP=$(hostname -I | awk '{print $1}')
print_status "Server IP: $SERVER_IP"
curl -f http://$SERVER_IP:8000/api/health/ 2>/dev/null && print_success "Backend accessible via server IP" || print_warning "Backend not accessible via server IP"

# Check port binding
print_status "Checking port bindings..."
netstat -tlnp | grep :8000 || print_warning "Port 8000 not bound"

# Check firewall status (if available)
print_status "Checking firewall status..."
if command -v ufw &> /dev/null; then
    ufw status
elif command -v firewall-cmd &> /dev/null; then
    firewall-cmd --list-ports
else
    print_warning "No common firewall tool found"
fi

# Check environment variables
print_status "Checking backend environment variables..."
docker exec $BACKEND_CONTAINER env | grep -E "(DJANGO_|DEBUG|ALLOWED_HOSTS|CORS_|DATABASE_URL)" | sort

# Test database connectivity
print_status "Testing database connectivity..."
docker exec $BACKEND_CONTAINER python manage.py check --database default && print_success "Database connection OK" || print_error "Database connection failed"

# Check Django URLs
print_status "Testing Django URL patterns..."
docker exec $BACKEND_CONTAINER python manage.py show_urls 2>/dev/null | head -10 || print_warning "Could not retrieve URL patterns"

print_status "Troubleshooting complete!"
print_status "If issues persist, check the logs above and ensure:"
print_status "1. All environment variables are correctly set"
print_status "2. Database is accessible and migrations are applied"
print_status "3. Firewall allows traffic on port 8000"
print_status "4. CORS settings include your client IP/domain"
