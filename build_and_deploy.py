#!/usr/bin/env python3
"""
Build and deployment script for GoID project
Handles Docker image building and pushing to Docker Hub
"""

import os
import sys
import subprocess
import json
from datetime import datetime
from pathlib import Path

class DockerBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        
        # Docker Hub configuration
        self.docker_hub_user = "aragawmebratu"  # Update if different
        self.backend_image = f"{self.docker_hub_user}/goid-production"
        self.frontend_image = f"{self.docker_hub_user}/goid-frontend"
        
        # Version tag (use timestamp or git commit)
        self.version = datetime.now().strftime("%Y%m%d-%H%M%S")
        
    def run_command(self, command, cwd=None, check=True):
        """Run a shell command and return the result"""
        print(f"🔄 Running: {command}")
        if cwd:
            print(f"   Working directory: {cwd}")
        
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=cwd, 
                check=check,
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                print(f"✅ Output: {result.stdout.strip()}")
            
            return result
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Command failed: {e}")
            if e.stdout:
                print(f"   Stdout: {e.stdout}")
            if e.stderr:
                print(f"   Stderr: {e.stderr}")
            raise
    
    def check_docker(self):
        """Check if Docker is available"""
        print("=" * 60)
        print("CHECKING DOCKER AVAILABILITY")
        print("=" * 60)
        
        try:
            result = self.run_command("docker --version")
            print("✅ Docker is available")
            
            # Check if Docker daemon is running
            self.run_command("docker info", check=False)
            print("✅ Docker daemon is running")
            
            return True
            
        except subprocess.CalledProcessError:
            print("❌ Docker is not available or not running")
            print("   Please install Docker and ensure it's running")
            return False
    
    def check_photo_processing(self):
        """Check photo processing functionality before building"""
        print("\n" + "=" * 60)
        print("CHECKING PHOTO PROCESSING FUNCTIONALITY")
        print("=" * 60)
        
        try:
            # Run the diagnostic script
            result = self.run_command(
                "python check_photo_processing.py", 
                cwd=self.backend_dir,
                check=False
            )
            
            if result.returncode == 0:
                print("✅ Photo processing check completed")
                return True
            else:
                print("⚠️  Photo processing check had issues")
                print("   Building anyway, but background removal may not work")
                return False
                
        except Exception as e:
            print(f"❌ Photo processing check failed: {e}")
            return False
    
    def build_backend(self):
        """Build backend Docker image"""
        print("\n" + "=" * 60)
        print("BUILDING BACKEND DOCKER IMAGE")
        print("=" * 60)
        
        # Check if Dockerfile exists
        dockerfile_path = self.backend_dir / "Dockerfile"
        if not dockerfile_path.exists():
            print(f"❌ Dockerfile not found at {dockerfile_path}")
            return False
        
        # Build command
        image_tag = f"{self.backend_image}:backend-{self.version}"
        latest_tag = f"{self.backend_image}:backend-latest"
        
        build_command = f"""
        docker build 
        -t {image_tag} 
        -t {latest_tag} 
        -f Dockerfile 
        .
        """.replace('\n', ' ').strip()
        
        try:
            self.run_command(build_command, cwd=self.backend_dir)
            print(f"✅ Backend image built: {image_tag}")
            return True
            
        except subprocess.CalledProcessError:
            print("❌ Backend build failed")
            return False
    
    def build_frontend(self):
        """Build frontend Docker image"""
        print("\n" + "=" * 60)
        print("BUILDING FRONTEND DOCKER IMAGE")
        print("=" * 60)
        
        # Check if Dockerfile exists
        dockerfile_path = self.frontend_dir / "Dockerfile"
        if not dockerfile_path.exists():
            print(f"❌ Dockerfile not found at {dockerfile_path}")
            return False
        
        # Build command
        image_tag = f"{self.frontend_image}:frontend-{self.version}"
        latest_tag = f"{self.frontend_image}:frontend-latest"
        
        build_command = f"""
        docker build 
        -t {image_tag} 
        -t {latest_tag} 
        -f Dockerfile 
        .
        """.replace('\n', ' ').strip()
        
        try:
            self.run_command(build_command, cwd=self.frontend_dir)
            print(f"✅ Frontend image built: {image_tag}")
            return True
            
        except subprocess.CalledProcessError:
            print("❌ Frontend build failed")
            return False
    
    def push_images(self):
        """Push images to Docker Hub"""
        print("\n" + "=" * 60)
        print("PUSHING IMAGES TO DOCKER HUB")
        print("=" * 60)
        
        # Check if logged in to Docker Hub
        try:
            self.run_command("docker info | grep Username", check=False)
        except:
            print("⚠️  You may need to login to Docker Hub:")
            print(f"   docker login")
            
        images_to_push = [
            f"{self.backend_image}:backend-{self.version}",
            f"{self.backend_image}:backend-latest",
            f"{self.frontend_image}:frontend-{self.version}",
            f"{self.frontend_image}:frontend-latest"
        ]
        
        success_count = 0
        for image in images_to_push:
            try:
                self.run_command(f"docker push {image}")
                print(f"✅ Pushed: {image}")
                success_count += 1
            except subprocess.CalledProcessError:
                print(f"❌ Failed to push: {image}")
        
        return success_count == len(images_to_push)
    
    def generate_deployment_info(self):
        """Generate deployment information"""
        print("\n" + "=" * 60)
        print("GENERATING DEPLOYMENT INFORMATION")
        print("=" * 60)
        
        deployment_info = {
            "build_time": datetime.now().isoformat(),
            "version": self.version,
            "images": {
                "backend": f"{self.backend_image}:backend-{self.version}",
                "frontend": f"{self.frontend_image}:frontend-{self.version}"
            },
            "deployment_commands": {
                "pull_backend": f"docker pull {self.backend_image}:backend-{self.version}",
                "pull_frontend": f"docker pull {self.frontend_image}:frontend-{self.version}",
                "update_compose": f"BACKEND_VERSION={self.version} FRONTEND_VERSION={self.version} docker-compose up -d"
            }
        }
        
        # Save to file
        info_file = self.project_root / f"deployment_info_{self.version}.json"
        with open(info_file, 'w') as f:
            json.dump(deployment_info, f, indent=2)
        
        print(f"✅ Deployment info saved to: {info_file}")
        print("\n📋 DEPLOYMENT COMMANDS FOR PRODUCTION SERVER:")
        print("=" * 60)
        print(f"# Pull new images:")
        print(f"docker pull {self.backend_image}:backend-{self.version}")
        print(f"docker pull {self.frontend_image}:frontend-{self.version}")
        print()
        print(f"# Update docker-compose and restart:")
        print(f"BACKEND_VERSION={self.version} FRONTEND_VERSION={self.version} docker-compose up -d")
        print()
        print("# Or update the docker-compose.yml file with the new version tags")
        
        return deployment_info

def main():
    """Main build and deployment function"""
    print("GoID Docker Build and Deployment Tool")
    print("=" * 60)
    
    builder = DockerBuilder()
    
    # Check prerequisites
    if not builder.check_docker():
        sys.exit(1)
    
    # Check photo processing (optional)
    builder.check_photo_processing()
    
    # Build images
    backend_success = builder.build_backend()
    frontend_success = builder.build_frontend()
    
    if not (backend_success and frontend_success):
        print("\n❌ Build failed. Please fix the issues and try again.")
        sys.exit(1)
    
    # Ask user if they want to push to Docker Hub
    push_choice = input("\n🤔 Push images to Docker Hub? (y/N): ").lower().strip()
    
    if push_choice in ['y', 'yes']:
        if builder.push_images():
            print("\n✅ All images pushed successfully!")
        else:
            print("\n⚠️  Some images failed to push")
    else:
        print("\n⏭️  Skipping Docker Hub push")
    
    # Generate deployment information
    deployment_info = builder.generate_deployment_info()
    
    print("\n" + "=" * 60)
    print("BUILD COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print(f"Version: {builder.version}")
    print("Next steps:")
    print("1. Copy the deployment commands above")
    print("2. SSH to your production server")
    print("3. Run the pull and update commands")

if __name__ == '__main__':
    main()
