# 🚀 Add Nginx to Your GoID Deployment

## Quick Instructions

You are currently on your server as root. Here's how to add nginx to your existing docker-compose.yml in the goid-deployment folder:

### Step 1: Navigate to the deployment folder
```bash
cd /home/<USER>/goid-deployment
```

### Step 2: Upload the nginx files
Upload these files to your goid-deployment folder:
- `nginx.conf`
- `add-nginx-to-existing.sh`

### Step 3: Run the script
```bash
chmod +x add-nginx-to-existing.sh
bash add-nginx-to-existing.sh
```

## What this script does:

1. ✅ **Backs up** your current docker-compose.yml
2. ✅ **Creates nginx.conf** with proper routing configuration
3. ✅ **Adds nginx service** to your existing docker-compose.yml
4. ✅ **Modifies frontend** to use internal port only
5. ✅ **Deploys the changes** with nginx on port 80

## Expected Results:

After running the script:
- ✅ **Port 80 will work**: `curl http://************` will return your frontend
- ✅ **API will work**: `curl http://************/api/common/language/info/` will return JSON
- ✅ **Admin will work**: `curl http://************/admin/` will redirect to login
- ✅ **No more CORS errors** in the browser console

## Architecture:

```
Internet → Port 80 → Nginx → {
    /api/* → Backend (Port 8000)
    /admin/* → Backend (Port 8000)
    /* → Frontend (Port 80)
}
```

## Rollback if needed:

If something goes wrong, you can rollback:
```bash
docker-compose down
cp docker-compose.yml.backup.* docker-compose.yml
docker-compose up -d
```

## Files you need:

1. **nginx.conf** - Nginx configuration for routing
2. **add-nginx-to-existing.sh** - Script to modify your docker-compose.yml

This will completely fix the API access issues you're experiencing!
