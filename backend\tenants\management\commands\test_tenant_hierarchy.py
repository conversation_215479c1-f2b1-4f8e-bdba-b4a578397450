"""
Management command to test tenant hierarchy data in ID card responses.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import Tenant, TenantWorkflowConfig
from idcards.models import IDCard, IDCardStatus
from citizens.models import Citizen
from users.models import User


class Command(BaseCommand):
    help = 'Test tenant hierarchy data in ID card responses'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=int,
            help='Test specific tenant ID (optional)',
        )
        parser.add_argument(
            '--id-card-id',
            type=int,
            help='Test specific ID card ID (optional)',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🧪 Testing Tenant Hierarchy Data in ID Card Responses'))
        self.stdout.write('=' * 70)
        
        tenant_id = options.get('tenant_id')
        id_card_id = options.get('id_card_id')
        
        if tenant_id and id_card_id:
            self.test_specific_id_card(tenant_id, id_card_id)
        elif tenant_id:
            self.test_tenant_id_cards(tenant_id)
        else:
            self.test_all_tenants()

    def test_specific_id_card(self, tenant_id, id_card_id):
        """Test a specific ID card."""
        try:
            tenant = Tenant.objects.get(id=tenant_id)
            self.stdout.write(f"\n🔍 Testing ID card {id_card_id} in tenant {tenant.name}")
            
            with schema_context(tenant.schema_name):
                try:
                    id_card = IDCard.objects.get(id=id_card_id)
                    self.stdout.write(f"✅ Found ID card: {id_card.card_number}")
                    self.stdout.write(f"   Status: {id_card.status}")
                    self.stdout.write(f"   Citizen: {id_card.citizen.get_full_name() if id_card.citizen else 'None'}")
                    
                    # Test what our enhanced retrieve method would return
                    card_data = {
                        'id': id_card.id,
                        'card_number': id_card.card_number,
                        'status': id_card.status,
                        'citizen_name': id_card.citizen.get_full_name() if id_card.citizen else 'Unknown'
                    }
                    
                    # Add tenant hierarchy information (same as our backend fix)
                    card_data['kebele_tenant'] = {
                        'id': tenant.id,
                        'name': tenant.name,
                        'schema_name': tenant.schema_name,
                        'parent_id': tenant.parent.id if tenant.parent else None,
                        'parent_name': tenant.parent.name if tenant.parent else None
                    }
                    
                    card_data['tenant_hierarchy'] = {
                        'kebele_tenant_id': tenant.id,
                        'subcity_tenant_id': tenant.parent.id if tenant.parent else None,
                        'city_tenant_id': tenant.parent.parent.id if tenant.parent and tenant.parent.parent else None
                    }
                    
                    self.stdout.write(f"📊 Tenant Hierarchy Data:")
                    self.stdout.write(f"   kebele_tenant_id: {card_data['tenant_hierarchy']['kebele_tenant_id']}")
                    self.stdout.write(f"   subcity_tenant_id: {card_data['tenant_hierarchy']['subcity_tenant_id']}")
                    self.stdout.write(f"   city_tenant_id: {card_data['tenant_hierarchy']['city_tenant_id']}")
                    
                    # Test if tenant data API would work with this data
                    self.test_tenant_data_api(card_data['tenant_hierarchy'])
                    
                except IDCard.DoesNotExist:
                    self.stdout.write(self.style.ERROR(f"❌ ID card {id_card_id} not found in tenant {tenant.name}"))
                    
        except Tenant.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"❌ Tenant {tenant_id} not found"))

    def test_tenant_id_cards(self, tenant_id):
        """Test all ID cards in a specific tenant."""
        try:
            tenant = Tenant.objects.get(id=tenant_id)
            self.stdout.write(f"\n🔍 Testing all ID cards in tenant {tenant.name}")
            
            with schema_context(tenant.schema_name):
                id_cards = IDCard.objects.all()[:5]  # Test first 5 cards
                self.stdout.write(f"📋 Found {IDCard.objects.count()} total ID cards, testing first 5")
                
                for id_card in id_cards:
                    self.stdout.write(f"\n   🆔 ID Card {id_card.id}: {id_card.card_number}")
                    self.stdout.write(f"      Status: {id_card.status}")
                    self.stdout.write(f"      Citizen: {id_card.citizen.get_full_name() if id_card.citizen else 'None'}")
                    
                    # Test tenant hierarchy data
                    tenant_hierarchy = {
                        'kebele_tenant_id': tenant.id,
                        'subcity_tenant_id': tenant.parent.id if tenant.parent else None,
                        'city_tenant_id': tenant.parent.parent.id if tenant.parent and tenant.parent.parent else None
                    }
                    
                    self.stdout.write(f"      Hierarchy: {tenant_hierarchy}")
                    
        except Tenant.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"❌ Tenant {tenant_id} not found"))

    def test_all_tenants(self):
        """Test ID cards from all tenants."""
        tenants = Tenant.objects.filter(type='kebele')[:3]  # Test first 3 kebeles
        self.stdout.write(f"🔍 Testing ID cards from {tenants.count()} kebele tenants")
        
        for tenant in tenants:
            self.stdout.write(f"\n🏢 Tenant: {tenant.name} (ID: {tenant.id})")
            self.stdout.write(f"   Schema: {tenant.schema_name}")
            self.stdout.write(f"   Parent: {tenant.parent.name if tenant.parent else 'None'}")
            
            try:
                with schema_context(tenant.schema_name):
                    id_card_count = IDCard.objects.count()
                    self.stdout.write(f"   📊 ID Cards: {id_card_count}")
                    
                    if id_card_count > 0:
                        # Test first ID card
                        id_card = IDCard.objects.first()
                        tenant_hierarchy = {
                            'kebele_tenant_id': tenant.id,
                            'subcity_tenant_id': tenant.parent.id if tenant.parent else None,
                            'city_tenant_id': tenant.parent.parent.id if tenant.parent and tenant.parent.parent else None
                        }
                        
                        self.stdout.write(f"   🔍 Sample hierarchy: {tenant_hierarchy}")
                        
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"   ❌ Error accessing {tenant.name}: {e}"))

    def test_tenant_data_api(self, tenant_hierarchy):
        """Test if the tenant data API would work with this hierarchy."""
        self.stdout.write(f"🧪 Testing tenant data API compatibility:")
        
        # Import the models that the API uses
        try:
            from tenants.models import Kebele, SubCity, CityAdministration
            
            kebele_id = tenant_hierarchy.get('kebele_tenant_id')
            subcity_id = tenant_hierarchy.get('subcity_tenant_id')
            city_id = tenant_hierarchy.get('city_tenant_id')
            
            # Test kebele data
            if kebele_id:
                try:
                    kebele = Kebele.objects.get(tenant_id=kebele_id)
                    self.stdout.write(f"   ✅ Kebele data available: {kebele.name}")
                    self.stdout.write(f"      Logo: {'Yes' if kebele.logo else 'No'}")
                    self.stdout.write(f"      Signature: {'Yes' if kebele.mayor_signature else 'No'}")
                    self.stdout.write(f"      Pattern: {'Yes' if kebele.pattern_image else 'No'}")
                except Kebele.DoesNotExist:
                    self.stdout.write(f"   ❌ Kebele data not found for tenant_id: {kebele_id}")
            
            # Test subcity data
            if subcity_id:
                try:
                    subcity = SubCity.objects.get(tenant_id=subcity_id)
                    self.stdout.write(f"   ✅ Subcity data available: {subcity.name}")
                    self.stdout.write(f"      Logo: {'Yes' if subcity.logo else 'No'}")
                    self.stdout.write(f"      Signature: {'Yes' if subcity.mayor_signature else 'No'}")
                    self.stdout.write(f"      Pattern: {'Yes' if subcity.pattern_image else 'No'}")
                except SubCity.DoesNotExist:
                    self.stdout.write(f"   ❌ Subcity data not found for tenant_id: {subcity_id}")
            
            # Test city data
            if city_id:
                try:
                    city = CityAdministration.objects.get(tenant_id=city_id)
                    self.stdout.write(f"   ✅ City data available: {city.city_name}")
                    self.stdout.write(f"      Logo: {'Yes' if city.logo else 'No'}")
                    self.stdout.write(f"      Signature: {'Yes' if city.mayor_signature else 'No'}")
                except CityAdministration.DoesNotExist:
                    self.stdout.write(f"   ❌ City data not found for tenant_id: {city_id}")
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"   ❌ Error testing tenant data API: {e}"))
