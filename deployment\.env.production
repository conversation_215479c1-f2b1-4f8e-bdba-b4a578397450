# GoID Production Environment Configuration
# Copy this file to .env in your deployment directory

# Database Configuration
DB_PASSWORD=goid_password_change_in_production

# Security Configuration
SECRET_KEY=change-this-secret-key-to-something-very-secure-in-production

# PgAdmin Configuration
PGADMIN_PASSWORD=admin123_change_in_production

# Network Configuration (adjust these to match your server)
SERVER_IP=************
DOMAIN=goid.uog.edu.et

# These will be automatically set based on the above
ALLOWED_HOSTS=************,goid.uog.edu.et,localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://************:3000,https://goid.uog.edu.et,http://goid.uog.edu.et:3000
