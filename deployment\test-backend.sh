#!/bin/bash

# Simple backend connectivity test script
echo "🔍 Testing GoID Backend Connectivity"
echo "===================================="

# Get server IP
SERVER_IP=$(hostname -I | awk '{print $1}')
echo "Server IP: $SERVER_IP"

# Test endpoints
echo ""
echo "Testing endpoints..."

# Test health endpoint (if it exists)
echo -n "Health endpoint: "
curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/health/ 2>/dev/null
echo ""

# Test admin endpoint
echo -n "Admin endpoint: "
curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/admin/ 2>/dev/null
echo ""

# Test API root
echo -n "API root: "
curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/ 2>/dev/null
echo ""

# Test with server IP
echo ""
echo "Testing with server IP ($SERVER_IP)..."

echo -n "Health endpoint via IP: "
curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP:8000/api/health/ 2>/dev/null
echo ""

echo -n "Admin endpoint via IP: "
curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP:8000/admin/ 2>/dev/null
echo ""

# Check if backend container is running
echo ""
echo "Backend container status:"
docker-compose ps backend

# Show recent logs
echo ""
echo "Recent backend logs:"
docker-compose logs --tail=10 backend
