#!/bin/bash

echo "🔍 Quick GoID Setup Check"
echo "========================"

echo "Current directory: $(pwd)"
echo ""

echo "Files in directory:"
ls -la
echo ""

if [ -f "docker-compose.yml" ]; then
    echo "✅ docker-compose.yml found"
    echo ""
    echo "Current docker-compose.yml content:"
    echo "=================================="
    cat docker-compose.yml
    echo "=================================="
    echo ""
    
    echo "Current running services:"
    docker-compose ps
    echo ""
    
    echo "Port bindings:"
    docker-compose ps | grep -E ":[0-9]+->" || echo "No port bindings found"
    echo ""
    
    # Check for nginx
    if grep -q "nginx:" docker-compose.yml; then
        echo "✅ Nginx service found in docker-compose.yml"
    else
        echo "❌ Nginx service NOT found in docker-compose.yml"
        echo ""
        echo "🚀 To fix API access issues, you need to add nginx reverse proxy"
        echo "   Run: bash deploy-nginx.sh"
    fi
    
    # Check for port 80
    if grep -q "80:80" docker-compose.yml; then
        echo "✅ Port 80 configured"
    else
        echo "❌ Port 80 NOT configured"
        echo "   This is why curl http://************ fails"
    fi
    
else
    echo "❌ docker-compose.yml NOT found"
    echo "Please navigate to the correct directory"
fi

echo ""
echo "Quick connectivity test:"
echo "Port 80: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:80 2>/dev/null || echo 'Connection refused')"
echo "Port 3000: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:3000 2>/dev/null || echo 'Connection refused')"
echo "Port 8000: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:8000 2>/dev/null || echo 'Connection refused')"
