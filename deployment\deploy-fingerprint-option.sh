#!/bin/bash

# Script to deploy fingerprint option feature on server
echo "Deploying Fingerprint Option Feature"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
DOCKER_HUB_USERNAME="aragawmebratu"
IMAGE_NAME="goid-production"
BACKEND_TAG="backend-fingerprint-option"
FRONTEND_TAG="frontend-fingerprint-option"

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found"
    print_error "Please run this script from the goid-deployment directory"
    exit 1
fi

print_success "Found docker-compose.yml"

# Show current service configuration
print_status "Current service configuration:"
echo "=============================="
grep -A 2 -B 1 "image.*goid-production" docker-compose.yml
echo "=============================="
echo ""

# Backup current docker-compose.yml
BACKUP_FILE="docker-compose.yml.fingerprint-option.backup.$(date +%Y%m%d_%H%M%S)"
print_status "Creating backup: $BACKUP_FILE"
cp docker-compose.yml "$BACKUP_FILE"

# Update the images in docker-compose.yml
print_status "Updating service images to use fingerprint option versions..."

# Replace backend image
sed -i "s|image: aragawmebratu/goid-production:backend-.*|image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG|g" docker-compose.yml

# Replace frontend image  
sed -i "s|image: aragawmebratu/goid-production:frontend-.*|image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG|g" docker-compose.yml

print_success "✅ Updated docker-compose.yml"

# Show updated configuration
print_status "Updated service configuration:"
echo "============================="
grep -A 2 -B 1 "image.*goid-production" docker-compose.yml
echo "============================="
echo ""

# Ask for confirmation
read -p "🚀 Deploy the fingerprint option feature? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Deployment cancelled"
    print_status "💾 Backup saved as: $BACKUP_FILE"
    exit 0
fi

# Pull the new images
print_status "Pulling new images from Docker Hub..."

print_status "Pulling backend image..."
docker pull "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
if [ $? -eq 0 ]; then
    print_success "✅ Successfully pulled backend image"
else
    print_error "❌ Failed to pull backend image"
    exit 1
fi

print_status "Pulling frontend image..."
docker pull "$DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"
if [ $? -eq 0 ]; then
    print_success "✅ Successfully pulled frontend image"
else
    print_error "❌ Failed to pull frontend image"
    exit 1
fi

# Stop services
print_status "Stopping services..."
docker-compose stop backend frontend

# Run database migrations
print_status "Running database migrations..."
docker-compose run --rm backend python manage.py migrate

if [ $? -eq 0 ]; then
    print_success "✅ Database migrations completed successfully"
else
    print_error "❌ Database migrations failed"
    print_error "Please check the logs and fix any migration issues"
    exit 1
fi

# Remove old containers
print_status "Removing old containers..."
docker-compose rm -f backend frontend

# Start services with new images
print_status "Starting services with new images..."
docker-compose up -d backend frontend

# Wait for services to start
print_status "Waiting for services to start..."
sleep 30

# Check service status
print_status "Checking service status..."
docker-compose ps backend frontend

# Test the services
print_status "Testing services..."
echo "Backend health: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:8000/admin/ 2>/dev/null || echo 'Failed')"
echo "Frontend health: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/ 2>/dev/null || echo 'Failed')"

# Test API endpoints
print_status "Testing API endpoints..."
echo "System settings: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/api/tenants/system-settings/ 2>/dev/null || echo 'Failed')"
echo "Kebele dashboard: $(curl -s -o /dev/null -w '%{http_code}' http://localhost/api/kebele-dashboard/ 2>/dev/null || echo 'Failed')"

# Show final status
echo ""
print_success "Fingerprint option feature deployed successfully!"
echo ""
print_status "📋 What was deployed:"
print_status "  ✅ Backend image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$BACKEND_TAG"
print_status "  ✅ Frontend image: $DOCKER_HUB_USERNAME/$IMAGE_NAME:$FRONTEND_TAG"
print_status "  ✅ Database migrations applied"
print_status "  ✅ Services restarted with new configuration"
echo ""
print_status "New Feature: Fingerprint Option"
print_status "  ✅ Citizens can now be registered with or without fingerprints"
print_status "  ✅ Option selection in biometric step of registration"
print_status "  ✅ Reason field for cases without fingerprints"
print_status "  ✅ Validation handles both scenarios appropriately"
echo ""
print_status "Test the new feature:"
print_status "  1. Go to citizen registration: http://************"
print_status "  2. Navigate to the biometric step (step 6)"
print_status "  3. You should see fingerprint option selection"
print_status "  4. Test both 'with fingerprint' and 'without fingerprint' options"
echo ""
print_status "💾 Backup saved as: $BACKUP_FILE"
print_status "🔄 To rollback if needed: cp $BACKUP_FILE docker-compose.yml && docker-compose up -d"

echo ""
print_success "The fingerprint option feature is now live!"
print_status "Citizens with medical conditions, disabilities, or other issues can now be registered without fingerprints."
