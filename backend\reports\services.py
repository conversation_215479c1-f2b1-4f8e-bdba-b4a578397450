from django.db import models
from django.utils import timezone
from django.db.models import Count, Q, Avg, Sum, F, Case, When, IntegerField
from django.contrib.auth import get_user_model
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
from decimal import Decimal

from tenants.models import Tenant
from django_tenants.utils import schema_context

User = get_user_model()


class ReportDataService:
    """Service for collecting and processing data for reports"""
    
    def __init__(self, tenant: Tenant):
        self.tenant = tenant
    
    def get_demographic_data(self, filters: Dict = None) -> Dict[str, Any]:
        """Get demographic distribution data"""
        filters = filters or {}
        
        with schema_context(self.tenant.schema_name):
            from citizens.models import Citizen
            from shared.models import Ketena
            
            # Base queryset
            queryset = Citizen.objects.all()
            
            # Apply filters
            if filters.get('date_from'):
                queryset = queryset.filter(created_at__gte=filters['date_from'])
            if filters.get('date_to'):
                queryset = queryset.filter(created_at__lte=filters['date_to'])
            if filters.get('kebele_id'):
                queryset = queryset.filter(kebele=filters['kebele_id'])
            if filters.get('ketena_id'):
                queryset = queryset.filter(ketena=filters['ketena_id'])
            
            total_citizens = queryset.count()
            
            # Gender distribution
            gender_data = queryset.values('gender').annotate(
                count=Count('id')
            ).order_by('gender')
            
            # Age group distribution
            today = timezone.now().date()
            age_groups = queryset.extra(
                select={
                    'age_group': """
                        CASE 
                            WHEN EXTRACT(year FROM age(date_of_birth)) < 18 THEN 'Under 18'
                            WHEN EXTRACT(year FROM age(date_of_birth)) BETWEEN 18 AND 30 THEN '18-30'
                            WHEN EXTRACT(year FROM age(date_of_birth)) BETWEEN 31 AND 50 THEN '31-50'
                            WHEN EXTRACT(year FROM age(date_of_birth)) BETWEEN 51 AND 65 THEN '51-65'
                            ELSE 'Over 65'
                        END
                    """
                }
            ).values('age_group').annotate(count=Count('id')).order_by('age_group')
            
            # Education level distribution
            education_data = queryset.values('education_level').annotate(
                count=Count('id')
            ).order_by('education_level')
            
            # Employment status distribution
            employment_data = queryset.values('employment').annotate(
                count=Count('id')
            ).order_by('employment')
            
            # Disability distribution
            disability_data = queryset.values('disability').annotate(
                count=Count('id')
            ).order_by('disability')
            
            # Location distribution
            location_data = queryset.values('kebele', 'ketena').annotate(
                count=Count('id')
            ).order_by('-count')
            
            return {
                'total_citizens': total_citizens,
                'gender_distribution': list(gender_data),
                'age_group_distribution': list(age_groups),
                'education_distribution': list(education_data),
                'employment_distribution': list(employment_data),
                'disability_distribution': list(disability_data),
                'location_distribution': list(location_data),
                'generated_at': timezone.now().isoformat(),
                'filters_applied': filters
            }
    
    def get_registration_trends(self, filters: Dict = None) -> Dict[str, Any]:
        """Get registration trends over time"""
        filters = filters or {}
        
        with schema_context(self.tenant.schema_name):
            from citizens.models import Citizen
            
            # Default to last 12 months if no date range specified
            end_date = filters.get('date_to', timezone.now().date())
            start_date = filters.get('date_from', end_date - timedelta(days=365))
            
            queryset = Citizen.objects.filter(
                created_at__date__gte=start_date,
                created_at__date__lte=end_date
            )
            
            # Apply additional filters
            if filters.get('kebele_id'):
                queryset = queryset.filter(kebele=filters['kebele_id'])
            
            # Daily registrations
            daily_registrations = queryset.extra(
                select={'day': 'date(created_at)'}
            ).values('day').annotate(
                count=Count('id')
            ).order_by('day')
            
            # Monthly registrations
            monthly_registrations = queryset.extra(
                select={'month': "to_char(created_at, 'YYYY-MM')"}
            ).values('month').annotate(
                count=Count('id')
            ).order_by('month')
            
            # Weekly registrations
            weekly_registrations = queryset.extra(
                select={'week': "to_char(created_at, 'YYYY-WW')"}
            ).values('week').annotate(
                count=Count('id')
            ).order_by('week')
            
            # Peak registration times (hour of day)
            hourly_distribution = queryset.extra(
                select={'hour': 'EXTRACT(hour FROM created_at)'}
            ).values('hour').annotate(
                count=Count('id')
            ).order_by('hour')
            
            # Registration by day of week
            daily_distribution = queryset.extra(
                select={'day_of_week': 'EXTRACT(dow FROM created_at)'}
            ).values('day_of_week').annotate(
                count=Count('id')
            ).order_by('day_of_week')
            
            total_registrations = queryset.count()
            avg_daily = total_registrations / max(1, (end_date - start_date).days)
            
            return {
                'total_registrations': total_registrations,
                'average_daily': round(avg_daily, 2),
                'period_start': start_date.isoformat(),
                'period_end': end_date.isoformat(),
                'daily_trends': list(daily_registrations),
                'monthly_trends': list(monthly_registrations),
                'weekly_trends': list(weekly_registrations),
                'hourly_distribution': list(hourly_distribution),
                'daily_distribution': list(daily_distribution),
                'generated_at': timezone.now().isoformat(),
                'filters_applied': filters
            }
    
    def get_id_card_status_report(self, filters: Dict = None) -> Dict[str, Any]:
        """Get ID card status and workflow analytics"""
        filters = filters or {}
        
        with schema_context(self.tenant.schema_name):
            from idcards.models import IDCard, IDCardStatus
            from citizens.models import Citizen
            
            # Base queryset
            queryset = IDCard.objects.all()
            
            # Apply filters
            if filters.get('date_from'):
                queryset = queryset.filter(created_at__gte=filters['date_from'])
            if filters.get('date_to'):
                queryset = queryset.filter(created_at__lte=filters['date_to'])
            if filters.get('kebele_id'):
                queryset = queryset.filter(citizen__kebele=filters['kebele_id'])
            
            total_cards = queryset.count()
            
            # Status distribution
            status_distribution = queryset.values('status').annotate(
                count=Count('id')
            ).order_by('status')
            
            # Processing time analysis
            completed_cards = queryset.filter(
                status__in=[IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
            )
            
            # Cards by creation month
            monthly_creation = queryset.extra(
                select={'month': "to_char(created_at, 'YYYY-MM')"}
            ).values('month').annotate(
                count=Count('id')
            ).order_by('month')
            
            # Expiring cards (next 30 days)
            thirty_days_from_now = timezone.now() + timedelta(days=30)
            expiring_cards = queryset.filter(
                expiry_date__lte=thirty_days_from_now,
                expiry_date__gte=timezone.now()
            ).count()
            
            # Expired cards
            expired_cards = queryset.filter(
                expiry_date__lt=timezone.now()
            ).count()
            
            # Citizens without ID cards
            total_citizens = Citizen.objects.count()
            citizens_with_cards = Citizen.objects.filter(
                id_cards__isnull=False
            ).distinct().count()
            citizens_without_cards = total_citizens - citizens_with_cards
            
            return {
                'total_id_cards': total_cards,
                'total_citizens': total_citizens,
                'citizens_with_cards': citizens_with_cards,
                'citizens_without_cards': citizens_without_cards,
                'coverage_percentage': round((citizens_with_cards / max(1, total_citizens)) * 100, 2),
                'status_distribution': list(status_distribution),
                'monthly_creation': list(monthly_creation),
                'expiring_cards': expiring_cards,
                'expired_cards': expired_cards,
                'generated_at': timezone.now().isoformat(),
                'filters_applied': filters
            }
    
    def get_service_access_analytics(self, filters: Dict = None) -> Dict[str, Any]:
        """Get analytics on service access using digital IDs"""
        filters = filters or {}
        
        with schema_context(self.tenant.schema_name):
            from idcards.models import IDCard
            from workflows.models import WorkflowLog
            
            # This would be expanded based on actual service integration
            # For now, we'll use workflow logs as a proxy for service access
            
            queryset = WorkflowLog.objects.all()
            
            if filters.get('date_from'):
                queryset = queryset.filter(timestamp__gte=filters['date_from'])
            if filters.get('date_to'):
                queryset = queryset.filter(timestamp__lte=filters['date_to'])
            
            # Service usage by action type
            service_usage = queryset.values('action').annotate(
                count=Count('id')
            ).order_by('-count')
            
            # Usage by user type
            user_type_usage = queryset.values(
                'performed_by__groups__name'
            ).annotate(
                count=Count('id')
            ).order_by('-count')
            
            # Daily service access
            daily_access = queryset.extra(
                select={'day': 'date(timestamp)'}
            ).values('day').annotate(
                count=Count('id')
            ).order_by('day')
            
            return {
                'total_service_interactions': queryset.count(),
                'service_usage_by_type': list(service_usage),
                'usage_by_user_type': list(user_type_usage),
                'daily_access_trends': list(daily_access),
                'generated_at': timezone.now().isoformat(),
                'filters_applied': filters
            }
    
    def get_migration_analysis(self, filters: Dict = None) -> Dict[str, Any]:
        """Get migration and transfer analytics"""
        filters = filters or {}
        
        with schema_context(self.tenant.schema_name):
            from workflows.models import CitizenTransferRequest, TransferStatus
            
            queryset = CitizenTransferRequest.objects.all()
            
            if filters.get('date_from'):
                queryset = queryset.filter(created_at__gte=filters['date_from'])
            if filters.get('date_to'):
                queryset = queryset.filter(created_at__lte=filters['date_to'])
            
            total_transfers = queryset.count()
            
            # Transfer status distribution
            status_distribution = queryset.values('status').annotate(
                count=Count('id')
            ).order_by('status')
            
            # Transfer trends by month
            monthly_transfers = queryset.extra(
                select={'month': "to_char(created_at, 'YYYY-MM')"}
            ).values('month').annotate(
                count=Count('id')
            ).order_by('month')
            
            # Source and destination analysis
            source_analysis = queryset.values('source_kebele').annotate(
                count=Count('id')
            ).order_by('-count')
            
            destination_analysis = queryset.values('destination_kebele').annotate(
                count=Count('id')
            ).order_by('-count')
            
            return {
                'total_transfers': total_transfers,
                'status_distribution': list(status_distribution),
                'monthly_trends': list(monthly_transfers),
                'top_source_kebeles': list(source_analysis[:10]),
                'top_destination_kebeles': list(destination_analysis[:10]),
                'generated_at': timezone.now().isoformat(),
                'filters_applied': filters
            }
