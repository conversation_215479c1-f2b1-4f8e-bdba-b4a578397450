# Safe migration to add fingerprint option fields
# This migration checks if columns exist before adding them

from django.db import migrations, models, connection


def add_fingerprint_fields_safe(apps, schema_editor):
    """Safely add fingerprint option fields if they don't exist"""
    
    with connection.cursor() as cursor:
        # Check if fingerprint_option column exists
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='citizens_citizen' 
            AND column_name='fingerprint_option'
        """)
        fingerprint_option_exists = cursor.fetchone() is not None
        
        # Check if no_fingerprint_reason column exists
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='citizens_citizen' 
            AND column_name='no_fingerprint_reason'
        """)
        no_fingerprint_reason_exists = cursor.fetchone() is not None
        
        # Add fingerprint_option if it doesn't exist
        if not fingerprint_option_exists:
            cursor.execute("""
                ALTER TABLE citizens_citizen 
                ADD COLUMN fingerprint_option VARCHAR(20) 
                DEFAULT 'with_fingerprint' NOT NULL
            """)
            print("✅ Added fingerprint_option column")
        else:
            print("ℹ️ fingerprint_option column already exists, skipping")
            
        # Add no_fingerprint_reason if it doesn't exist
        if not no_fingerprint_reason_exists:
            cursor.execute("""
                ALTER TABLE citizens_citizen 
                ADD COLUMN no_fingerprint_reason TEXT
            """)
            print("✅ Added no_fingerprint_reason column")
        else:
            print("ℹ️ no_fingerprint_reason column already exists, skipping")


def remove_fingerprint_fields(apps, schema_editor):
    """Remove fingerprint option fields"""
    
    with connection.cursor() as cursor:
        # Remove columns if they exist
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='citizens_citizen' 
            AND column_name IN ('fingerprint_option', 'no_fingerprint_reason')
        """)
        existing_columns = [row[0] for row in cursor.fetchall()]
        
        if 'fingerprint_option' in existing_columns:
            cursor.execute("ALTER TABLE citizens_citizen DROP COLUMN fingerprint_option")
            print("🗑️ Removed fingerprint_option column")
            
        if 'no_fingerprint_reason' in existing_columns:
            cursor.execute("ALTER TABLE citizens_citizen DROP COLUMN no_fingerprint_reason")
            print("🗑️ Removed no_fingerprint_reason column")


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0005_fix_ethiopian_dates'),
    ]

    operations = [
        migrations.RunPython(add_fingerprint_fields_safe, remove_fingerprint_fields),
    ]
